<variant
    name="release"
    package="com.appystore.mrecharge"
    minSdkVersion="24"
    targetSdkVersion="35"
    mergedManifest="build\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.10.1;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.14\transforms\839ab44d898c93cde484be25e300c581\transformed\desugar_jdk_libs_configuration-2.1.3-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.14\transforms\f9d6071d3a87451a98a1d0c7279bb7dc\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\release\compileReleaseJavaWithJavac\classes;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.appystore.mrecharge"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out"
      generatedResourceFolders="build\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.14\transforms\839ab44d898c93cde484be25e300c581\transformed\desugar_jdk_libs_configuration-2.1.3-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.14\transforms\f9d6071d3a87451a98a1d0c7279bb7dc\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
