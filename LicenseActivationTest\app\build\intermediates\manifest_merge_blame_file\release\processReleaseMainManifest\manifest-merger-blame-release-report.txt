1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.appystore.mrecharge"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-feature
11-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:5:5-7:36
12        android:name="android.hardware.telephony"
12-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:6:9-50
13        android:required="false" />
13-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:7:9-33
14
15    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:9:5-80
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:10:5-78
16-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:10:22-76
17    <uses-permission android:name="android.permission.INTERNET" />
17-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:11:5-66
17-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:11:22-64
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:12:5-75
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:12:22-73
19    <uses-permission android:name="android.permission.READ_SMS" />
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:13:5-66
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:13:22-64
20    <uses-permission android:name="android.permission.SEND_SMS" />
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:14:5-66
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:14:22-64
21    <uses-permission android:name="android.permission.RECEIVE_SMS" />
21-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:15:5-69
21-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:15:22-67
22    <uses-permission android:name="android.permission.CALL_PHONE" />
22-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:16:5-68
22-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:16:22-66
23    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:17:5-74
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:17:22-72
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:18:5-76
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:18:22-74
25    <uses-permission android:name="android.permission.WAKE_LOCK" />
25-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:19:5-67
25-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:19:22-65
26    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
26-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:20:5-74
26-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:20:22-72
27    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:21:5-94
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:21:22-92
28    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:22:5-77
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:22:22-75
29    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:23:5-81
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:23:22-79
30    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:24:5-109
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:24:22-107
31    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
31-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:23:5-77
31-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:23:22-74
32    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
32-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\435d61ecbbe3481154f76d1f2bd8d528\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
32-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\435d61ecbbe3481154f76d1f2bd8d528\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
33
34    <permission
34-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
35        android:name="com.appystore.mrecharge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.appystore.mrecharge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
39
40    <application
40-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:26:5-308:19
41        android:allowBackup="true"
41-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:27:9-35
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
43        android:dataExtractionRules="@xml/data_extraction_rules"
43-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:28:9-65
44        android:extractNativeLibs="false"
45        android:fullBackupContent="@xml/backup_rules"
45-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:29:9-54
46        android:icon="@mipmap/ic_launcher"
46-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:30:9-43
47        android:label="@string/app_name"
47-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:31:9-41
48        android:networkSecurityConfig="@xml/network_security_config"
48-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:35:9-69
49        android:roundIcon="@mipmap/ic_launcher"
49-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:32:9-48
50        android:supportsRtl="true"
50-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:33:9-35
51        android:theme="@style/Theme.AppyStoreMRecharge"
51-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:34:9-56
52        android:usesCleartextTraffic="true" >
52-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:36:9-44
53        <uses-library
53-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:38:9-40:39
54            android:name="org.apache.http.legacy"
54-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:39:13-50
55            android:required="false" />
55-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:40:13-37
56
57        <activity
57-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:41:9-49:20
58            android:name="com.appystore.mrecharge.activity.MainActivity"
58-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:44:13-73
59            android:exported="true"
59-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:42:13-36
60            android:label="@string/app_name" >
60-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:43:13-45
61            <intent-filter>
61-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:45:13-48:29
62                <action android:name="android.intent.action.MAIN" />
62-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:46:17-68
62-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:46:25-66
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:47:17-76
64-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:47:27-74
65            </intent-filter>
66        </activity>
67
68        <receiver
68-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:50:9-55:20
69            android:name="com.appystore.mrecharge.IncomingSms"
69-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:50:19-69
70            android:exported="true" >
70-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:51:13-36
71            <intent-filter android:priority="1000" >
71-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:52:13-54:29
71-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:52:28-51
72                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
72-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:53:17-81
72-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:53:25-79
73            </intent-filter>
74        </receiver>
75
76        <service
76-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:56:9-61:19
77            android:name="com.appystore.mrecharge.service.PushService"
77-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:56:18-76
78            android:exported="false" >
78-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:57:13-37
79            <intent-filter>
79-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:58:13-60:29
80                <action android:name="com.google.firebase.MESSAGING_EVENT" />
80-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:59:17-77
80-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:59:25-75
81            </intent-filter>
82        </service>
83        <service android:name="com.appystore.mrecharge.service.Recharge" />
83-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:62:9-75
83-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:62:18-73
84        <service
84-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:63:9-67:43
85            android:name="com.appystore.mrecharge.service.sever"
85-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:64:13-65
86            android:enabled="true"
86-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:65:13-35
87            android:exported="true"
87-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:66:13-36
88            android:stopWithTask="false" />
88-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:67:13-41
89        <service android:name="com.appystore.mrecharge.service.Smsend" />
89-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:68:9-73
89-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:68:18-71
90        <service android:name="com.appystore.mrecharge.service.International_service" />
90-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:69:9-88
90-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:69:18-86
91        <service
91-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:70:9-80:19
92            android:name="com.appystore.mrecharge.service.USSDService"
92-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:71:13-71
93            android:exported="true"
93-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:73:13-36
94            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
94-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:72:13-79
95            <intent-filter>
95-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:74:13-76:29
96                <action android:name="android.accessibilityservice.AccessibilityService" />
96-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:75:17-91
96-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:75:25-89
97            </intent-filter>
98
99            <meta-data
99-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:77:13-79:55
100                android:name="android.accessibilityservice"
100-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:78:17-60
101                android:resource="@xml/ussd_service" />
101-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:79:17-53
102        </service>
103
104        <activity
104-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:81:9-102
105            android:name="com.appystore.mrecharge.activity.Settings"
105-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:81:19-75
106            android:exported="false" />
106-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:81:76-100
107        <activity
107-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:82:9-104
108            android:name="com.appystore.mrecharge.activity.intsetting"
108-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:82:19-77
109            android:exported="false" />
109-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:82:78-102
110        <activity
110-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:83:9-104
111            android:name="com.appystore.mrecharge.activity.Monitoring"
111-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:83:19-77
112            android:exported="false" />
112-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:83:78-102
113
114        <provider
114-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:84:9-91:20
115            android:name="androidx.startup.InitializationProvider"
115-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:85:13-67
116            android:authorities="com.appystore.mrecharge.androidx-startup"
116-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:87:13-75
117            android:exported="false" >
117-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:86:13-37
118            <meta-data
118-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:88:13-90:51
119                android:name="androidx.work.WorkManagerInitializer"
119-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:89:17-68
120                android:value="androidx.startup" />
120-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:90:17-49
121            <meta-data
121-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aa38a909f3c22e0015a23f181d9b4b3b\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.emoji2.text.EmojiCompatInitializer"
122-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aa38a909f3c22e0015a23f181d9b4b3b\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
123                android:value="androidx.startup" />
123-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\aa38a909f3c22e0015a23f181d9b4b3b\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
124            <meta-data
124-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8f1fc009573b7426ba9e8f3843153ff\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
125-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8f1fc009573b7426ba9e8f3843153ff\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
126                android:value="androidx.startup" />
126-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8f1fc009573b7426ba9e8f3843153ff\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
127            <meta-data
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
129                android:value="androidx.startup" />
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
130        </provider>
131
132        <service
132-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:92:9-96:46
133            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
133-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:93:13-88
134            android:directBootAware="false"
134-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:96:13-44
135            android:enabled="@bool/enable_system_alarm_service_default"
135-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:94:13-72
136            android:exported="false" />
136-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:95:13-37
137        <service
137-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:97:9-102:46
138            android:name="androidx.work.impl.background.systemjob.SystemJobService"
138-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:98:13-84
139            android:directBootAware="false"
139-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:102:13-44
140            android:enabled="@bool/enable_system_job_service_default"
140-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:100:13-70
141            android:exported="true"
141-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:101:13-36
142            android:permission="android.permission.BIND_JOB_SERVICE" />
142-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:99:13-69
143        <service
143-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:103:9-107:46
144            android:name="androidx.work.impl.foreground.SystemForegroundService"
144-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:104:13-81
145            android:directBootAware="false"
145-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:107:13-44
146            android:enabled="@bool/enable_system_foreground_service_default"
146-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:105:13-77
147            android:exported="false" />
147-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:106:13-37
148
149        <receiver
149-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:108:9-112:46
150            android:name="androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver"
150-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:109:13-88
151            android:directBootAware="false"
151-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:112:13-44
152            android:enabled="true"
152-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:110:13-35
153            android:exported="false" />
153-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:111:13-37
154        <receiver
154-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:113:9-122:20
155            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy"
155-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:114:13-106
156            android:directBootAware="false"
156-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:117:13-44
157            android:enabled="false"
157-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:115:13-36
158            android:exported="false" >
158-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:116:13-37
159            <intent-filter>
159-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:118:13-121:29
160                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
160-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:119:17-86
160-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:119:25-84
161                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
161-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:120:17-89
161-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:120:25-87
162            </intent-filter>
163        </receiver>
164        <receiver
164-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:123:9-132:20
165            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy"
165-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:124:13-104
166            android:directBootAware="false"
166-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:127:13-44
167            android:enabled="false"
167-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:125:13-36
168            android:exported="false" >
168-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:126:13-37
169            <intent-filter>
169-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:128:13-131:29
170                <action android:name="android.intent.action.BATTERY_OKAY" />
170-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:129:17-76
170-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:129:25-74
171                <action android:name="android.intent.action.BATTERY_LOW" />
171-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:17-75
171-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:25-73
172            </intent-filter>
173        </receiver>
174        <receiver
174-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:133:9-142:20
175            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy"
175-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:134:13-104
176            android:directBootAware="false"
176-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:137:13-44
177            android:enabled="false"
177-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:135:13-36
178            android:exported="false" >
178-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:136:13-37
179            <intent-filter>
179-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:138:13-141:29
180                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
180-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:139:17-82
180-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:139:25-80
181                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
181-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:17-81
181-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:25-79
182            </intent-filter>
183        </receiver>
184        <receiver
184-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:143:9-151:20
185            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy"
185-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:144:13-103
186            android:directBootAware="false"
186-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:147:13-44
187            android:enabled="false"
187-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:145:13-36
188            android:exported="false" >
188-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:146:13-37
189            <intent-filter>
189-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:148:13-150:29
190                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
190-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:149:17-78
190-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:149:25-76
191            </intent-filter>
192        </receiver>
193        <receiver
193-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:152:9-162:20
194            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
194-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:153:13-88
195            android:directBootAware="false"
195-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:156:13-44
196            android:enabled="false"
196-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:154:13-36
197            android:exported="false" >
197-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:155:13-37
198            <intent-filter>
198-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:157:13-161:29
199                <action android:name="android.intent.action.BOOT_COMPLETED" />
199-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:158:17-78
199-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:158:25-76
200                <action android:name="android.intent.action.TIME_SET" />
200-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:159:17-72
200-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:159:25-70
201                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
201-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:160:17-80
201-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:160:25-78
202            </intent-filter>
203        </receiver>
204        <receiver
204-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:163:9-171:20
205            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
205-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:164:13-99
206            android:directBootAware="false"
206-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:167:13-44
207            android:enabled="@bool/enable_system_alarm_service_default"
207-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:165:13-72
208            android:exported="false" >
208-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:166:13-37
209            <intent-filter>
209-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:168:13-170:29
210                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
210-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:169:17-97
210-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:169:25-95
211            </intent-filter>
212        </receiver>
213        <receiver
213-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:172:9-181:20
214            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
214-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:173:13-78
215            android:directBootAware="false"
215-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:177:13-44
216            android:enabled="true"
216-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:175:13-35
217            android:exported="true"
217-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:176:13-36
218            android:permission="android.permission.DUMP" >
218-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:174:13-57
219            <intent-filter>
219-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:178:13-180:29
220                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
220-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:179:17-87
220-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:179:25-85
221            </intent-filter>
222        </receiver>
223
224        <activity
224-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:182:9-197:20
225            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
225-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:184:13-80
226            android:excludeFromRecents="true"
226-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:186:13-46
227            android:exported="true"
227-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:185:13-36
228            android:launchMode="singleTask"
228-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:187:13-44
229            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
229-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:183:13-72
230            <intent-filter>
230-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:188:13-196:29
231                <action android:name="android.intent.action.VIEW" />
231-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:189:17-68
231-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:189:25-66
232
233                <category android:name="android.intent.category.DEFAULT" />
233-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:17-75
233-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:27-73
234                <category android:name="android.intent.category.BROWSABLE" />
234-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:191:17-77
234-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:191:27-75
235
236                <data
236-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:192:17-195:39
237                    android:host="firebase.auth"
237-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:194:21-49
238                    android:path="/"
238-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:195:21-37
239                    android:scheme="genericidp" />
239-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:193:21-48
240            </intent-filter>
241        </activity>
242        <activity
242-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:198:9-213:20
243            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
243-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:200:13-79
244            android:excludeFromRecents="true"
244-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:202:13-46
245            android:exported="true"
245-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:201:13-36
246            android:launchMode="singleTask"
246-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:203:13-44
247            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
247-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:199:13-72
248            <intent-filter>
248-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:204:13-212:29
249                <action android:name="android.intent.action.VIEW" />
249-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:189:17-68
249-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:189:25-66
250
251                <category android:name="android.intent.category.DEFAULT" />
251-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:17-75
251-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:27-73
252                <category android:name="android.intent.category.BROWSABLE" />
252-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:191:17-77
252-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:191:27-75
253
254                <data
254-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:192:17-195:39
255                    android:host="firebase.auth"
255-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:194:21-49
256                    android:path="/"
256-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:195:21-37
257                    android:scheme="recaptcha" />
257-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:193:21-48
258            </intent-filter>
259        </activity>
260
261        <service
261-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:214:9-222:19
262            android:name="com.google.firebase.auth.api.fallback.service.FirebaseAuthFallbackService"
262-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:215:13-101
263            android:enabled="true"
263-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:216:13-35
264            android:exported="false" >
264-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:217:13-37
265            <intent-filter>
265-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:218:13-221:29
266                <action android:name="com.google.firebase.auth.api.gms.service.START" />
266-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:219:17-88
266-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:219:25-86
267
268                <category android:name="android.intent.category.DEFAULT" />
268-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:17-75
268-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:27-73
269            </intent-filter>
270        </service>
271        <service
271-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:223:9-245:19
272            android:name="com.google.firebase.components.ComponentDiscoveryService"
272-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:224:13-84
273            android:directBootAware="true"
273-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:226:13-43
274            android:exported="false" >
274-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:225:13-37
275            <meta-data
275-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:227:13-229:84
276                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
276-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:228:17-109
277                android:value="com.google.firebase.components.ComponentRegistrar" />
277-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:229:17-82
278            <meta-data
278-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:230:13-232:84
279                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
279-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:231:17-115
280                android:value="com.google.firebase.components.ComponentRegistrar" />
280-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:232:17-82
281            <meta-data
281-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:233:13-235:84
282                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
282-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:234:17-119
283                android:value="com.google.firebase.components.ComponentRegistrar" />
283-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:235:17-82
284            <meta-data
284-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:236:13-238:84
285                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
285-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:237:17-115
286                android:value="com.google.firebase.components.ComponentRegistrar" />
286-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:238:17-82
287            <meta-data
287-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:239:13-241:84
288                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
288-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:240:17-139
289                android:value="com.google.firebase.components.ComponentRegistrar" />
289-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:241:17-82
290            <meta-data
290-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:242:13-244:84
291                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
291-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:243:17-127
292                android:value="com.google.firebase.components.ComponentRegistrar" />
292-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:244:17-82
293            <meta-data
293-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:57:13-59:85
294                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
294-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:58:17-122
295                android:value="com.google.firebase.components.ComponentRegistrar" />
295-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:59:17-82
296            <meta-data
296-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\501be6f5311fc7674eaab5b094c8d259\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
297                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
297-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\501be6f5311fc7674eaab5b094c8d259\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
298                android:value="com.google.firebase.components.ComponentRegistrar" />
298-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\501be6f5311fc7674eaab5b094c8d259\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
299            <meta-data
299-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a2ee6c6ec182e85729b567b0981fb038\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
300                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
300-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a2ee6c6ec182e85729b567b0981fb038\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
301                android:value="com.google.firebase.components.ComponentRegistrar" />
301-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a2ee6c6ec182e85729b567b0981fb038\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
302            <meta-data
302-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07f678a0ca4907834db1d485ccc0617e\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
303                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
303-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07f678a0ca4907834db1d485ccc0617e\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
304                android:value="com.google.firebase.components.ComponentRegistrar" />
304-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07f678a0ca4907834db1d485ccc0617e\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
305        </service>
306
307        <receiver
307-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:246:9-253:20
308            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
308-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:247:13-78
309            android:exported="true"
309-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:249:13-36
310            android:permission="com.google.android.c2dm.permission.SEND" >
310-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:248:13-73
311            <intent-filter>
311-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:250:13-252:29
312                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
312-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:251:17-80
312-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:251:25-78
313            </intent-filter>
314
315            <meta-data
315-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:37:13-39:40
316                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
316-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:38:17-92
317                android:value="true" />
317-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\AndroidManifest.xml:39:17-37
318        </receiver>
319
320        <service
320-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:254:9-261:19
321            android:name="com.google.firebase.messaging.FirebaseMessagingService"
321-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:255:13-82
322            android:directBootAware="true"
322-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:257:13-43
323            android:exported="false" >
323-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:256:13-37
324            <intent-filter android:priority="-500" >
324-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:58:13-60:29
324-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:258:28-51
325                <action android:name="com.google.firebase.MESSAGING_EVENT" />
325-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:59:17-77
325-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:59:25-75
326            </intent-filter>
327        </service>
328        <service
328-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:262:9-268:19
329            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
329-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:263:13-103
330            android:exported="false" >
330-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:264:13-37
331            <meta-data
331-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:265:13-267:38
332                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
332-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:266:17-94
333                android:value="cct" />
333-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:267:17-36
334        </service>
335
336        <provider
336-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:269:9-274:45
337            android:name="com.google.firebase.provider.FirebaseInitProvider"
337-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:270:13-77
338            android:authorities="com.appystore.mrecharge.firebaseinitprovider"
338-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:272:13-79
339            android:directBootAware="true"
339-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:274:13-43
340            android:exported="false"
340-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:271:13-37
341            android:initOrder="100" />
341-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:273:13-36
342
343        <activity
343-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:275:9-278:39
344            android:name="com.google.android.gms.common.api.GoogleApiActivity"
344-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:277:13-79
345            android:exported="false"
345-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:278:13-37
346            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
346-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:276:13-72
347
348        <receiver
348-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:279:9-282:39
349            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
349-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:280:13-85
350            android:enabled="true"
350-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:281:13-35
351            android:exported="false" />
351-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:282:13-37
352
353        <service
353-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:283:9-286:39
354            android:name="com.google.android.gms.measurement.AppMeasurementService"
354-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:284:13-84
355            android:enabled="true"
355-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:285:13-35
356            android:exported="false" />
356-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:286:13-37
357        <service
357-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:287:9-291:39
358            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
358-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:288:13-87
359            android:enabled="true"
359-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:290:13-35
360            android:exported="false"
360-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:291:13-37
361            android:permission="android.permission.BIND_JOB_SERVICE" />
361-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:289:13-69
362
363        <meta-data
363-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:292:9-294:68
364            android:name="com.google.android.gms.version"
364-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:293:13-58
365            android:value="@integer/google_play_services_version" />
365-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:294:13-66
366
367        <service
367-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:295:9-298:45
368            android:name="androidx.room.MultiInstanceInvalidationService"
368-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:296:13-74
369            android:directBootAware="true"
369-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:298:13-43
370            android:exported="false" />
370-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:297:13-37
371        <service
371-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:299:9-302:39
372            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
372-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:300:13-117
373            android:exported="false"
373-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:302:13-37
374            android:permission="android.permission.BIND_JOB_SERVICE" />
374-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:301:13-69
375
376        <receiver
376-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:303:9-305:39
377            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
377-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:304:13-132
378            android:exported="false" />
378-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:305:13-37
379
380        <service
380-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
381            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
381-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
382            android:enabled="true"
382-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
383            android:exported="false" >
383-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
384            <meta-data
384-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
385                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
385-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
386                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
386-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
387        </service>
388
389        <activity
389-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
390            android:name="androidx.credentials.playservices.HiddenActivity"
390-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
391            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
391-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
392            android:enabled="true"
392-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
393            android:exported="false"
393-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
394            android:fitsSystemWindows="true"
394-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
395            android:theme="@style/Theme.Hidden" >
395-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
396        </activity>
397        <activity
397-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
398            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
398-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
399            android:excludeFromRecents="true"
399-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
400            android:exported="false"
400-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
401            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
401-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
402        <!--
403            Service handling Google Sign-In user revocation. For apps that do not integrate with
404            Google Sign-In, this service will never be started.
405        -->
406        <service
406-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
407            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
407-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
408            android:exported="true"
408-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
409            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
409-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
410            android:visibleToInstantApps="true" />
410-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
411
412        <receiver
412-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
413            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
413-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
414            android:directBootAware="false"
414-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
415            android:enabled="true"
415-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
416            android:exported="false" />
416-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
417        <receiver
417-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
418            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
418-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
419            android:directBootAware="false"
419-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
420            android:enabled="false"
420-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
421            android:exported="false" >
421-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
422            <intent-filter>
422-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:118:13-121:29
423                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
423-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:119:17-86
423-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:119:25-84
424                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
424-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:120:17-89
424-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:120:25-87
425            </intent-filter>
426        </receiver>
427        <receiver
427-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
428            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
428-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
429            android:directBootAware="false"
429-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
430            android:enabled="false"
430-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
431            android:exported="false" >
431-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
432            <intent-filter>
432-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:128:13-131:29
433                <action android:name="android.intent.action.BATTERY_OKAY" />
433-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:129:17-76
433-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:129:25-74
434                <action android:name="android.intent.action.BATTERY_LOW" />
434-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:17-75
434-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:25-73
435            </intent-filter>
436        </receiver>
437        <receiver
437-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
438            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
438-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
439            android:directBootAware="false"
439-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
440            android:enabled="false"
440-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
441            android:exported="false" >
441-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
442            <intent-filter>
442-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:138:13-141:29
443                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
443-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:139:17-82
443-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:139:25-80
444                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
444-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:17-81
444-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:25-79
445            </intent-filter>
446        </receiver>
447        <receiver
447-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
448            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
448-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
449            android:directBootAware="false"
449-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
450            android:enabled="false"
450-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
451            android:exported="false" >
451-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
452            <intent-filter>
452-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:148:13-150:29
453                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
453-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:149:17-78
453-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:149:25-76
454            </intent-filter>
455        </receiver>
456        <receiver
456-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
457            android:name="androidx.profileinstaller.ProfileInstallReceiver"
457-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
458            android:directBootAware="false"
458-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
459            android:enabled="true"
459-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
460            android:exported="true"
460-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
461            android:permission="android.permission.DUMP" >
461-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
462            <intent-filter>
462-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
463                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
463-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
463-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
464            </intent-filter>
465            <intent-filter>
465-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
466                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
466-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
466-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
467            </intent-filter>
468            <intent-filter>
468-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
469                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
469-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
469-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
470            </intent-filter>
471            <intent-filter>
471-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
472                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
472-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
472-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
473            </intent-filter>
474        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
475        <activity
475-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\27726287844e0c57051862f89d0a94a5\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
476            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
476-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\27726287844e0c57051862f89d0a94a5\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
477            android:exported="false"
477-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\27726287844e0c57051862f89d0a94a5\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
478            android:stateNotNeeded="true"
478-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\27726287844e0c57051862f89d0a94a5\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
479            android:theme="@style/Theme.PlayCore.Transparent" />
479-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\27726287844e0c57051862f89d0a94a5\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
480    </application>
481
482</manifest>
