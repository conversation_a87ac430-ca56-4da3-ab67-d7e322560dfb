<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.google.firebase.auth.api.fallback.service.FirebaseAuthFallbackService`, was not found in the project or the libraries">
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="215"
            column="27"
            startOffset="10763"
            endLine="215"
            endColumn="100"
            endOffset="10836"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.google.android.gms.measurement.AppMeasurementReceiver`, was not found in the project or the libraries">
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="280"
            column="27"
            startOffset="14378"
            endLine="280"
            endColumn="84"
            endOffset="14435"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.google.android.gms.measurement.AppMeasurementService`, was not found in the project or the libraries">
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="284"
            column="27"
            startOffset="14558"
            endLine="284"
            endColumn="83"
            endOffset="14614"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.google.android.gms.measurement.AppMeasurementJobService`, was not found in the project or the libraries">
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="288"
            column="27"
            startOffset="14737"
            endLine="288"
            endColumn="86"
            endOffset="14796"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `androidx.room.MultiInstanceInvalidationService`, was not found in the project or the libraries">
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="296"
            column="27"
            startOffset="15137"
            endLine="296"
            endColumn="73"
            endOffset="15183"/>
    </incident>

    <incident
        id="ScrollViewSize"
        severity="warning"
        message="This LinearLayout should use `android:layout_height=&quot;wrap_content&quot;`">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="88"
            column="13"
            startOffset="3854"
            endLine="88"
            endColumn="49"
            endOffset="3890"/>
    </incident>

    <incident
        id="ScrollViewSize"
        severity="warning"
        message="This LinearLayout should use `android:layout_height=&quot;wrap_content&quot;`">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="118"
            column="13"
            startOffset="4507"
            endLine="118"
            endColumn="49"
            endOffset="4543"/>
    </incident>

    <incident
        id="ScrollViewSize"
        severity="warning"
        message="This LinearLayout should use `android:layout_height=&quot;wrap_content&quot;`">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="92"
            column="13"
            startOffset="3995"
            endLine="92"
            endColumn="49"
            endOffset="4031"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="497"
            column="14"
            startOffset="20428"
            endLine="497"
            endColumn="22"
            endOffset="20436"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/Dialfunction.java"
            line="61"
            column="22"
            startOffset="2292"
            endLine="61"
            endColumn="30"
            endOffset="2300"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/Dialfunction.java"
            line="156"
            column="14"
            startOffset="6234"
            endLine="156"
            endColumn="22"
            endOffset="6242"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/Dialfunction.java"
            line="179"
            column="30"
            startOffset="7825"
            endLine="179"
            endColumn="38"
            endOffset="7833"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="341"
            column="22"
            startOffset="14813"
            endLine="341"
            endColumn="30"
            endOffset="14821"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="374"
            column="22"
            startOffset="16229"
            endLine="374"
            endColumn="30"
            endOffset="16237"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="412"
            column="22"
            startOffset="18110"
            endLine="412"
            endColumn="30"
            endOffset="18118"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="422"
            column="22"
            startOffset="18662"
            endLine="422"
            endColumn="30"
            endOffset="18670"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="475"
            column="22"
            startOffset="21441"
            endLine="475"
            endColumn="30"
            endOffset="21449"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="608"
            column="14"
            startOffset="27047"
            endLine="608"
            endColumn="22"
            endOffset="27055"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Monitoring.java"
            line="97"
            column="14"
            startOffset="4143"
            endLine="97"
            endColumn="22"
            endOffset="4151"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Tes.java"
            line="303"
            column="14"
            startOffset="12975"
            endLine="303"
            endColumn="22"
            endOffset="12983"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="532"
            column="14"
            startOffset="19998"
            endLine="532"
            endColumn="22"
            endOffset="20006"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="536"
            column="99"
            startOffset="20160"
            endLine="536"
            endColumn="107"
            endOffset="20168"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/intsetting.java"
            line="83"
            column="22"
            startOffset="3682"
            endLine="83"
            endColumn="30"
            endOffset="3690"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/intsetting.java"
            line="101"
            column="22"
            startOffset="4607"
            endLine="101"
            endColumn="30"
            endOffset="4615"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/intsetting.java"
            line="119"
            column="22"
            startOffset="5533"
            endLine="119"
            endColumn="30"
            endOffset="5541"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/intsetting.java"
            line="137"
            column="22"
            startOffset="6459"
            endLine="137"
            endColumn="30"
            endOffset="6467"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/intsetting.java"
            line="155"
            column="22"
            startOffset="7377"
            endLine="155"
            endColumn="30"
            endOffset="7385"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/intsetting.java"
            line="181"
            column="14"
            startOffset="8698"
            endLine="181"
            endColumn="22"
            endOffset="8706"/>
    </incident>

    <incident
        id="CutPasteId"
        severity="warning"
        message="The id `R.id.licensekey` has already been looked up in this method; possible cut &amp; paste error?">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="265"
            column="31"
            startOffset="11385"
            endLine="265"
            endColumn="60"
            endOffset="11414"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="158"
            column="35"
            startOffset="5928"
            endLine="158"
            endColumn="64"
            endOffset="5957"
            message="First usage here"/>
    </incident>

    <incident
        id="CutPasteId"
        severity="warning"
        message="The id `R.id.dpin` has already been looked up in this method; possible cut &amp; paste error?">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="266"
            column="32"
            startOffset="11447"
            endLine="266"
            endColumn="55"
            endOffset="11470"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="159"
            column="36"
            startOffset="5994"
            endLine="159"
            endColumn="59"
            endOffset="6017"
            message="First usage here"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="595"
            column="23"
            startOffset="26440"
            endLine="595"
            endColumn="55"
            endOffset="26472"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Tes.java"
            line="44"
            column="141"
            startOffset="1768"
            endLine="44"
            endColumn="152"
            endOffset="1779"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Tes.java"
            line="77"
            column="62"
            startOffset="3453"
            endLine="77"
            endColumn="73"
            endOffset="3464"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Tes.java"
            line="95"
            column="69"
            startOffset="4428"
            endLine="95"
            endColumn="80"
            endOffset="4439"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Tes.java"
            line="98"
            column="66"
            startOffset="4771"
            endLine="98"
            endColumn="77"
            endOffset="4782"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Tes.java"
            line="218"
            column="38"
            startOffset="9882"
            endLine="218"
            endColumn="49"
            endOffset="9893"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Tes.java"
            line="242"
            column="38"
            startOffset="10940"
            endLine="242"
            endColumn="49"
            endOffset="10951"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Tes.java"
            line="275"
            column="48"
            startOffset="11899"
            endLine="275"
            endColumn="59"
            endOffset="11910"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="156"
            column="32"
            startOffset="5257"
            endLine="156"
            endColumn="43"
            endOffset="5268"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="157"
            column="32"
            startOffset="5322"
            endLine="157"
            endColumn="43"
            endOffset="5333"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="158"
            column="32"
            startOffset="5390"
            endLine="158"
            endColumn="43"
            endOffset="5401"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="159"
            column="32"
            startOffset="5459"
            endLine="159"
            endColumn="43"
            endOffset="5470"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="160"
            column="32"
            startOffset="5522"
            endLine="160"
            endColumn="43"
            endOffset="5533"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="161"
            column="32"
            startOffset="5587"
            endLine="161"
            endColumn="43"
            endOffset="5598"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="162"
            column="32"
            startOffset="5658"
            endLine="162"
            endColumn="43"
            endOffset="5669"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="246"
            column="41"
            startOffset="8263"
            endLine="246"
            endColumn="52"
            endOffset="8274"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="314"
            column="53"
            startOffset="10522"
            endLine="314"
            endColumn="64"
            endOffset="10533"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="314"
            column="87"
            startOffset="10556"
            endLine="314"
            endColumn="98"
            endOffset="10567"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="358"
            column="69"
            startOffset="12251"
            endLine="358"
            endColumn="80"
            endOffset="12262"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="389"
            column="41"
            startOffset="13683"
            endLine="389"
            endColumn="52"
            endOffset="13694"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="389"
            column="87"
            startOffset="13729"
            endLine="389"
            endColumn="98"
            endOffset="13740"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="405"
            column="127"
            startOffset="14765"
            endLine="405"
            endColumn="138"
            endOffset="14776"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="405"
            column="174"
            startOffset="14812"
            endLine="405"
            endColumn="185"
            endOffset="14823"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="481"
            column="38"
            startOffset="17934"
            endLine="481"
            endColumn="49"
            endOffset="17945"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="504"
            column="38"
            startOffset="18964"
            endLine="504"
            endColumn="49"
            endOffset="18975"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="514"
            column="62"
            startOffset="19321"
            endLine="514"
            endColumn="73"
            endOffset="19332"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="562"
            column="54"
            startOffset="20874"
            endLine="562"
            endColumn="65"
            endOffset="20885"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="210"
            column="35"
            startOffset="8899"
            endLine="210"
            endColumn="66"
            endOffset="8930"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="229"
            column="41"
            startOffset="9561"
            endLine="229"
            endColumn="86"
            endOffset="9606"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="230"
            column="41"
            startOffset="9649"
            endLine="230"
            endColumn="86"
            endOffset="9694"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="231"
            column="41"
            startOffset="9737"
            endLine="231"
            endColumn="84"
            endOffset="9780"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="319"
            column="48"
            startOffset="13032"
            endLine="319"
            endColumn="93"
            endOffset="13077"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="320"
            column="50"
            startOffset="13129"
            endLine="320"
            endColumn="83"
            endOffset="13162"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="321"
            column="48"
            startOffset="13212"
            endLine="321"
            endColumn="93"
            endOffset="13257"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="322"
            column="47"
            startOffset="13306"
            endLine="322"
            endColumn="77"
            endOffset="13336"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="323"
            column="41"
            startOffset="13379"
            endLine="323"
            endColumn="82"
            endOffset="13420"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="436"
            column="35"
            startOffset="18516"
            endLine="436"
            endColumn="63"
            endOffset="18544"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="453"
            column="35"
            startOffset="19011"
            endLine="453"
            endColumn="63"
            endOffset="19039"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/Dialfunction.java"
            line="49"
            column="54"
            startOffset="1659"
            endLine="49"
            endColumn="102"
            endOffset="1707"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/Dialfunction.java"
            line="55"
            column="34"
            startOffset="1964"
            endLine="55"
            endColumn="63"
            endOffset="1993"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/Recharge.java"
            line="83"
            column="51"
            startOffset="2662"
            endLine="83"
            endColumn="83"
            endOffset="2694"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/Recharge.java"
            line="84"
            column="52"
            startOffset="2748"
            endLine="84"
            endColumn="81"
            endOffset="2777"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/Recharge.java"
            line="85"
            column="51"
            startOffset="2830"
            endLine="85"
            endColumn="80"
            endOffset="2859"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/Recharge.java"
            line="86"
            column="46"
            startOffset="2907"
            endLine="86"
            endColumn="96"
            endOffset="2957"/>
    </incident>

    <incident
        id="ShowToast"
        severity="warning"
        message="Expected duration `Toast.LENGTH_SHORT` or `Toast.LENGTH_LONG`, a custom duration value is not supported">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/Dialfunction.java"
            line="117"
            column="68"
            startOffset="4538"
            endLine="117"
            endColumn="69"
            endOffset="4539"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/util/NotificationUtils.java"
            line="172"
            column="39"
            startOffset="6851"
            endLine="172"
            endColumn="82"
            endOffset="6894"/>
    </incident>

    <incident
        id="WrongConstant"
        severity="error"
        message="Must be one of: Context.POWER_SERVICE, Context.WINDOW_SERVICE, Context.LAYOUT_INFLATER_SERVICE, Context.ACCOUNT_SERVICE, Context.ACTIVITY_SERVICE, Context.ALARM_SERVICE, Context.NOTIFICATION_SERVICE, Context.ACCESSIBILITY_SERVICE, Context.CAPTIONING_SERVICE, Context.KEYGUARD_SERVICE, Context.LOCATION_SERVICE, Context.HEALTHCONNECT_SERVICE, Context.SEARCH_SERVICE, Context.SENSOR_SERVICE, Context.STORAGE_SERVICE, Context.STORAGE_STATS_SERVICE, Context.WALLPAPER_SERVICE, Context.VIBRATOR_MANAGER_SERVICE, Context.VIBRATOR_SERVICE, Context.CONNECTIVITY_SERVICE, Context.IPSEC_SERVICE, Context.VPN_MANAGEMENT_SERVICE, Context.NETWORK_STATS_SERVICE, Context.WIFI_SERVICE, Context.WIFI_AWARE_SERVICE, Context.WIFI_P2P_SERVICE, Context.WIFI_RTT_RANGING_SERVICE, Context.NSD_SERVICE, Context.AUDIO_SERVICE, Context.FINGERPRINT_SERVICE, Context.BIOMETRIC_SERVICE, Context.MEDIA_ROUTER_SERVICE, Context.TELEPHONY_SERVICE, Context.TELEPHONY_SUBSCRIPTION_SERVICE, Context.CARRIER_CONFIG_SERVICE, Context.EUICC_SERVICE, Context.TELECOM_SERVICE, Context.CLIPBOARD_SERVICE, Context.INPUT_METHOD_SERVICE, Context.TEXT_SERVICES_MANAGER_SERVICE, Context.TEXT_CLASSIFICATION_SERVICE, Context.APPWIDGET_SERVICE, Context.DROPBOX_SERVICE, Context.DEVICE_POLICY_SERVICE, Context.UI_MODE_SERVICE, Context.DOWNLOAD_SERVICE, Context.NFC_SERVICE, Context.BLUETOOTH_SERVICE, Context.USB_SERVICE, Context.LAUNCHER_APPS_SERVICE, Context.INPUT_SERVICE, Context.DISPLAY_SERVICE, Context.USER_SERVICE, Context.RESTRICTIONS_SERVICE, Context.APP_OPS_SERVICE, Context.ROLE_SERVICE, Context.CAMERA_SERVICE, Context.PRINT_SERVICE, Context.CONSUMER_IR_SERVICE, Context.TV_INTERACTIVE_APP_SERVICE, Context.TV_INPUT_SERVICE, Context.USAGE_STATS_SERVICE, Context.MEDIA_SESSION_SERVICE, Context.MEDIA_COMMUNICATION_SERVICE, Context.BATTERY_SERVICE, Context.JOB_SCHEDULER_SERVICE, Context.PERSISTENT_DATA_BLOCK_SERVICE, Context.MEDIA_PROJECTION_SERVICE, Context.MIDI_SERVICE, Context.HARDWARE_PROPERTIES_SERVICE, Context.SHORTCUT_SERVICE, Context.SYSTEM_HEALTH_SERVICE, Context.COMPANION_DEVICE_SERVICE, Context.VIRTUAL_DEVICE_SERVICE, Context.CROSS_PROFILE_APPS_SERVICE, Context.LOCALE_SERVICE, Context.MEDIA_METRICS_SERVICE, Context.DISPLAY_HASH_SERVICE, Context.CREDENTIAL_SERVICE, Context.DEVICE_LOCK_SERVICE, Context.GRAMMATICAL_INFLECTION_SERVICE, Context.SECURITY_STATE_SERVICE, Context.CONTACT_KEYS_SERVICE">
        <fix-replace
            description="Change to Context.CONNECTIVITY_SERVICE (connectivity)"
            replacement="android.content.Context.CONNECTIVITY_SERVICE"
            shortenNames="true"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="470"
            column="89"
            startOffset="19473"
            endLine="470"
            endColumn="103"
            endOffset="19487"/>
    </incident>

    <incident
        id="WrongConstant"
        severity="error"
        message="Must be one of: Toast.LENGTH_SHORT, Toast.LENGTH_LONG">
        <fix-alternatives>
            <fix-replace
                description="Change to Toast.LENGTH_SHORT (0)"
                replacement="android.widget.Toast.LENGTH_SHORT"
                shortenNames="true"
                priority="0"/>
            <fix-replace
                description="Change to Toast.LENGTH_LONG"
                replacement="android.widget.Toast.LENGTH_LONG"
                shortenNames="true"
                priority="0"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/Dialfunction.java"
            line="117"
            column="68"
            startOffset="4538"
            endLine="117"
            endColumn="69"
            endOffset="4539"/>
    </incident>

    <incident
        id="WrongConstant"
        severity="error"
        message="Must be one of: Context.POWER_SERVICE, Context.WINDOW_SERVICE, Context.LAYOUT_INFLATER_SERVICE, Context.ACCOUNT_SERVICE, Context.ACTIVITY_SERVICE, Context.ALARM_SERVICE, Context.NOTIFICATION_SERVICE, Context.ACCESSIBILITY_SERVICE, Context.CAPTIONING_SERVICE, Context.KEYGUARD_SERVICE, Context.LOCATION_SERVICE, Context.HEALTHCONNECT_SERVICE, Context.SEARCH_SERVICE, Context.SENSOR_SERVICE, Context.STORAGE_SERVICE, Context.STORAGE_STATS_SERVICE, Context.WALLPAPER_SERVICE, Context.VIBRATOR_MANAGER_SERVICE, Context.VIBRATOR_SERVICE, Context.CONNECTIVITY_SERVICE, Context.IPSEC_SERVICE, Context.VPN_MANAGEMENT_SERVICE, Context.NETWORK_STATS_SERVICE, Context.WIFI_SERVICE, Context.WIFI_AWARE_SERVICE, Context.WIFI_P2P_SERVICE, Context.WIFI_RTT_RANGING_SERVICE, Context.NSD_SERVICE, Context.AUDIO_SERVICE, Context.FINGERPRINT_SERVICE, Context.BIOMETRIC_SERVICE, Context.MEDIA_ROUTER_SERVICE, Context.TELEPHONY_SERVICE, Context.TELEPHONY_SUBSCRIPTION_SERVICE, Context.CARRIER_CONFIG_SERVICE, Context.EUICC_SERVICE, Context.TELECOM_SERVICE, Context.CLIPBOARD_SERVICE, Context.INPUT_METHOD_SERVICE, Context.TEXT_SERVICES_MANAGER_SERVICE, Context.TEXT_CLASSIFICATION_SERVICE, Context.APPWIDGET_SERVICE, Context.DROPBOX_SERVICE, Context.DEVICE_POLICY_SERVICE, Context.UI_MODE_SERVICE, Context.DOWNLOAD_SERVICE, Context.NFC_SERVICE, Context.BLUETOOTH_SERVICE, Context.USB_SERVICE, Context.LAUNCHER_APPS_SERVICE, Context.INPUT_SERVICE, Context.DISPLAY_SERVICE, Context.USER_SERVICE, Context.RESTRICTIONS_SERVICE, Context.APP_OPS_SERVICE, Context.ROLE_SERVICE, Context.CAMERA_SERVICE, Context.PRINT_SERVICE, Context.CONSUMER_IR_SERVICE, Context.TV_INTERACTIVE_APP_SERVICE, Context.TV_INPUT_SERVICE, Context.USAGE_STATS_SERVICE, Context.MEDIA_SESSION_SERVICE, Context.MEDIA_COMMUNICATION_SERVICE, Context.BATTERY_SERVICE, Context.JOB_SCHEDULER_SERVICE, Context.PERSISTENT_DATA_BLOCK_SERVICE, Context.MEDIA_PROJECTION_SERVICE, Context.MIDI_SERVICE, Context.HARDWARE_PROPERTIES_SERVICE, Context.SHORTCUT_SERVICE, Context.SYSTEM_HEALTH_SERVICE, Context.COMPANION_DEVICE_SERVICE, Context.VIRTUAL_DEVICE_SERVICE, Context.CROSS_PROFILE_APPS_SERVICE, Context.LOCALE_SERVICE, Context.MEDIA_METRICS_SERVICE, Context.DISPLAY_HASH_SERVICE, Context.CREDENTIAL_SERVICE, Context.DEVICE_LOCK_SERVICE, Context.GRAMMATICAL_INFLECTION_SERVICE, Context.SECURITY_STATE_SERVICE, Context.CONTACT_KEYS_SERVICE">
        <fix-replace
            description="Change to Context.ACTIVITY_SERVICE (activity)"
            replacement="android.content.Context.ACTIVITY_SERVICE"
            shortenNames="true"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="612"
            column="95"
            startOffset="27213"
            endLine="612"
            endColumn="105"
            endOffset="27223"/>
    </incident>

    <incident
        id="WrongConstant"
        severity="error"
        message="Must be one of: Context.POWER_SERVICE, Context.WINDOW_SERVICE, Context.LAYOUT_INFLATER_SERVICE, Context.ACCOUNT_SERVICE, Context.ACTIVITY_SERVICE, Context.ALARM_SERVICE, Context.NOTIFICATION_SERVICE, Context.ACCESSIBILITY_SERVICE, Context.CAPTIONING_SERVICE, Context.KEYGUARD_SERVICE, Context.LOCATION_SERVICE, Context.HEALTHCONNECT_SERVICE, Context.SEARCH_SERVICE, Context.SENSOR_SERVICE, Context.STORAGE_SERVICE, Context.STORAGE_STATS_SERVICE, Context.WALLPAPER_SERVICE, Context.VIBRATOR_MANAGER_SERVICE, Context.VIBRATOR_SERVICE, Context.CONNECTIVITY_SERVICE, Context.IPSEC_SERVICE, Context.VPN_MANAGEMENT_SERVICE, Context.NETWORK_STATS_SERVICE, Context.WIFI_SERVICE, Context.WIFI_AWARE_SERVICE, Context.WIFI_P2P_SERVICE, Context.WIFI_RTT_RANGING_SERVICE, Context.NSD_SERVICE, Context.AUDIO_SERVICE, Context.FINGERPRINT_SERVICE, Context.BIOMETRIC_SERVICE, Context.MEDIA_ROUTER_SERVICE, Context.TELEPHONY_SERVICE, Context.TELEPHONY_SUBSCRIPTION_SERVICE, Context.CARRIER_CONFIG_SERVICE, Context.EUICC_SERVICE, Context.TELECOM_SERVICE, Context.CLIPBOARD_SERVICE, Context.INPUT_METHOD_SERVICE, Context.TEXT_SERVICES_MANAGER_SERVICE, Context.TEXT_CLASSIFICATION_SERVICE, Context.APPWIDGET_SERVICE, Context.DROPBOX_SERVICE, Context.DEVICE_POLICY_SERVICE, Context.UI_MODE_SERVICE, Context.DOWNLOAD_SERVICE, Context.NFC_SERVICE, Context.BLUETOOTH_SERVICE, Context.USB_SERVICE, Context.LAUNCHER_APPS_SERVICE, Context.INPUT_SERVICE, Context.DISPLAY_SERVICE, Context.USER_SERVICE, Context.RESTRICTIONS_SERVICE, Context.APP_OPS_SERVICE, Context.ROLE_SERVICE, Context.CAMERA_SERVICE, Context.PRINT_SERVICE, Context.CONSUMER_IR_SERVICE, Context.TV_INTERACTIVE_APP_SERVICE, Context.TV_INPUT_SERVICE, Context.USAGE_STATS_SERVICE, Context.MEDIA_SESSION_SERVICE, Context.MEDIA_COMMUNICATION_SERVICE, Context.BATTERY_SERVICE, Context.JOB_SCHEDULER_SERVICE, Context.PERSISTENT_DATA_BLOCK_SERVICE, Context.MEDIA_PROJECTION_SERVICE, Context.MIDI_SERVICE, Context.HARDWARE_PROPERTIES_SERVICE, Context.SHORTCUT_SERVICE, Context.SYSTEM_HEALTH_SERVICE, Context.COMPANION_DEVICE_SERVICE, Context.VIRTUAL_DEVICE_SERVICE, Context.CROSS_PROFILE_APPS_SERVICE, Context.LOCALE_SERVICE, Context.MEDIA_METRICS_SERVICE, Context.DISPLAY_HASH_SERVICE, Context.CREDENTIAL_SERVICE, Context.DEVICE_LOCK_SERVICE, Context.GRAMMATICAL_INFLECTION_SERVICE, Context.SECURITY_STATE_SERVICE, Context.CONTACT_KEYS_SERVICE">
        <fix-replace
            description="Change to Context.ACCESSIBILITY_SERVICE (accessibility)"
            replacement="android.content.Context.ACCESSIBILITY_SERVICE"
            shortenNames="true"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="650"
            column="98"
            startOffset="28606"
            endLine="650"
            endColumn="113"
            endOffset="28621"/>
    </incident>

    <incident
        id="WrongConstant"
        severity="error"
        message="Must be one or more of: AccessibilityServiceInfo.FEEDBACK_AUDIBLE, AccessibilityServiceInfo.FEEDBACK_GENERIC, AccessibilityServiceInfo.FEEDBACK_HAPTIC, AccessibilityServiceInfo.FEEDBACK_SPOKEN, AccessibilityServiceInfo.FEEDBACK_VISUAL, AccessibilityServiceInfo.FEEDBACK_BRAILLE">
        <fix-alternatives>
            <fix-replace
                description="Change to AccessibilityServiceInfo.FEEDBACK_GENERIC (16)"
                replacement="android.accessibilityservice.AccessibilityServiceInfo.FEEDBACK_GENERIC"
                shortenNames="true"
                priority="0"/>
            <fix-replace
                description="Change to AccessibilityServiceInfo.FEEDBACK_AUDIBLE"
                replacement="android.accessibilityservice.AccessibilityServiceInfo.FEEDBACK_AUDIBLE"
                shortenNames="true"
                priority="0"/>
            <fix-replace
                description="Change to AccessibilityServiceInfo.FEEDBACK_HAPTIC"
                replacement="android.accessibilityservice.AccessibilityServiceInfo.FEEDBACK_HAPTIC"
                shortenNames="true"
                priority="0"/>
            <fix-replace
                description="Change to AccessibilityServiceInfo.FEEDBACK_SPOKEN"
                replacement="android.accessibilityservice.AccessibilityServiceInfo.FEEDBACK_SPOKEN"
                shortenNames="true"
                priority="0"/>
            <fix-replace
                description="Change to AccessibilityServiceInfo.FEEDBACK_VISUAL"
                replacement="android.accessibilityservice.AccessibilityServiceInfo.FEEDBACK_VISUAL"
                shortenNames="true"
                priority="0"/>
            <fix-replace
                description="Change to AccessibilityServiceInfo.FEEDBACK_BRAILLE"
                replacement="android.accessibilityservice.AccessibilityServiceInfo.FEEDBACK_BRAILLE"
                shortenNames="true"
                priority="0"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="445"
            column="49"
            startOffset="16364"
            endLine="445"
            endColumn="51"
            endOffset="16366"/>
    </incident>

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="43"
            column="13"
            startOffset="2243"
            endLine="43"
            endColumn="45"
            endOffset="2275"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.10.1 is available: 8.11.0">
        <fix-replace
            description="Change to 8.11.0"
            family="Update versions"
            oldString="8.10.1"
            replacement="8.11.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="15"
            endOffset="25"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.android.tools:desugar_jdk_libs than 2.1.3 is available: 2.1.5">
        <fix-replace
            description="Change to 2.1.5"
            family="Update versions"
            oldString="2.1.3"
            replacement="2.1.5"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="34"
            column="27"
            startOffset="805"
            endLine="34"
            endColumn="69"
            endOffset="847"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.squareup.okhttp3:okhttp than 4.11.0 is available: 4.12.0">
        <fix-replace
            description="Change to 4.12.0"
            family="Update versions"
            oldString="4.11.0"
            replacement="4.12.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="48"
            column="20"
            startOffset="1237"
            endLine="48"
            endColumn="56"
            endOffset="1273"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.squareup.okhttp3:logging-interceptor than 4.11.0 is available: 4.12.0">
        <fix-replace
            description="Change to 4.12.0"
            family="Update versions"
            oldString="4.11.0"
            replacement="4.12.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="49"
            column="20"
            startOffset="1293"
            endLine="49"
            endColumn="69"
            endOffset="1342"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.annotation:annotation than 1.7.1 is available: 1.9.1">
        <fix-replace
            description="Change to 1.9.1"
            family="Update versions"
            oldString="1.7.1"
            replacement="1.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="52"
            column="20"
            startOffset="1410"
            endLine="52"
            endColumn="58"
            endOffset="1448"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.7.0"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="13"
            startOffset="101"
            endLine="6"
            endColumn="20"
            endOffset="108"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-messaging than 24.0.0 is available: 24.1.2">
        <fix-replace
            description="Change to 24.1.2"
            family="Update versions"
            oldString="24.0.0"
            replacement="24.1.2"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="13"
            column="22"
            startOffset="259"
            endLine="13"
            endColumn="30"
            endOffset="267"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.8.0 is available: 33.16.0">
        <fix-replace
            description="Change to 33.16.0"
            family="Update versions"
            oldString="32.8.0"
            replacement="33.16.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="14"
            column="16"
            startOffset="283"
            endLine="14"
            endColumn="24"
            endOffset="291"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.gms:play-services-base than 18.3.0 is available: 18.7.0">
        <fix-replace
            description="Change to 18.7.0"
            family="Update versions"
            oldString="18.3.0"
            replacement="18.7.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="15"
            column="22"
            startOffset="313"
            endLine="15"
            endColumn="30"
            endOffset="321"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.work:work-runtime than 2.9.0 is available: 2.10.2">
        <fix-replace
            description="Change to 2.10.2"
            family="Update versions"
            oldString="2.9.0"
            replacement="2.10.2"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="16"
            column="16"
            startOffset="337"
            endLine="16"
            endColumn="23"
            endOffset="344"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-auth than 23.2.0 is available: 23.2.1">
        <fix-replace
            description="Change to 23.2.1"
            family="Update versions"
            oldString="23.2.0"
            replacement="23.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="19"
            column="16"
            startOffset="409"
            endLine="19"
            endColumn="24"
            endOffset="417"/>
    </incident>

    <incident
        id="NotConstructor"
        severity="warning"
        message="Method Devicer looks like a constructor but is a normal method">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/util/Devicer.java"
            line="15"
            column="5"
            startOffset="376"
            endLine="17"
            endColumn="295"
            endOffset="733"/>
    </incident>

    <incident
        id="InnerclassSeparator"
        severity="warning"
        message="Use &apos;$&apos; instead of &apos;.&apos; for inner classes; replace &quot;androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver&quot; with &quot;androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver&quot;">
        <fix-replace
            description="Replace with androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            robot="true"
            independent="true"
            oldString="androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver"
            replacement="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="109"
            column="27"
            startOffset="5687"
            endLine="109"
            endColumn="87"
            endOffset="5747"/>
    </incident>

    <incident
        id="InnerclassSeparator"
        severity="warning"
        message="Use &apos;$&apos; instead of &apos;.&apos; for inner classes; replace &quot;androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy&quot; with &quot;androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy&quot;">
        <fix-replace
            description="Replace with androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            robot="true"
            independent="true"
            oldString="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy"
            replacement="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="114"
            column="27"
            startOffset="5916"
            endLine="114"
            endColumn="105"
            endOffset="5994"/>
    </incident>

    <incident
        id="InnerclassSeparator"
        severity="warning"
        message="Use &apos;$&apos; instead of &apos;.&apos; for inner classes; replace &quot;androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy&quot; with &quot;androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy&quot;">
        <fix-replace
            description="Replace with androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            robot="true"
            independent="true"
            oldString="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy"
            replacement="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="124"
            column="27"
            startOffset="6420"
            endLine="124"
            endColumn="103"
            endOffset="6496"/>
    </incident>

    <incident
        id="InnerclassSeparator"
        severity="warning"
        message="Use &apos;$&apos; instead of &apos;.&apos; for inner classes; replace &quot;androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy&quot; with &quot;androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy&quot;">
        <fix-replace
            description="Replace with androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            robot="true"
            independent="true"
            oldString="androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy"
            replacement="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="134"
            column="27"
            startOffset="6898"
            endLine="134"
            endColumn="103"
            endOffset="6974"/>
    </incident>

    <incident
        id="InnerclassSeparator"
        severity="warning"
        message="Use &apos;$&apos; instead of &apos;.&apos; for inner classes; replace &quot;androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy&quot; with &quot;androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy&quot;">
        <fix-replace
            description="Replace with androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            robot="true"
            independent="true"
            oldString="androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy"
            replacement="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="144"
            column="27"
            startOffset="7388"
            endLine="144"
            endColumn="102"
            endOffset="7463"/>
    </incident>

    <incident
        id="PxUsage"
        severity="warning"
        message="Avoid using &quot;`px`&quot; as units; use &quot;`dp`&quot; instead">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/custom_dialog.xml"
            line="7"
            column="9"
            startOffset="262"
            endLine="7"
            endColumn="37"
            endOffset="290"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `getString` to get device identifiers is not recommended">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="1371"
            column="45"
            startOffset="62910"
            endLine="1371"
            endColumn="124"
            endOffset="62989"/>
    </incident>

    <incident
        id="UnprotectedSMSBroadcastReceiver"
        severity="warning"
        message="BroadcastReceivers that declare an intent-filter for `SMS_DELIVER` or `SMS_RECEIVED` must ensure that the caller has the `BROADCAST_SMS` permission, otherwise it is possible for malicious actors to spoof intents">
        <fix-attribute
            description="Set permission=&quot;android.permission.BROADCAST_SMS&quot;"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="permission"
            value="android.permission.BROADCAST_SMS"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="50"
            column="10"
            startOffset="2587"
            endLine="50"
            endColumn="18"
            endOffset="2595"/>
    </incident>

    <incident
        id="ExportedService"
        severity="warning"
        message="Exported service does not require permission">
        <fix-alternatives>
            <fix-attribute
                description="Set permission"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="permission"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set exported=&quot;false&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="exported"
                value="false"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="63"
            column="10"
            startOffset="3230"
            endLine="63"
            endColumn="17"
            endOffset="3237"/>
    </incident>

    <incident
        id="InsecureBaseConfiguration"
        severity="warning"
        message="Insecure Base Configuration">
        <fix-replace
            description="Replace with false"
            replacement="false"
            priority="0">
            <range
                file="${:app*release*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
                startOffset="109"
                endOffset="113"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
            line="3"
            column="45"
            startOffset="109"
            endLine="3"
            endColumn="49"
            endOffset="113"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Monitoring.java"
            line="137"
            column="21"
            startOffset="6305"
            endLine="137"
            endColumn="72"
            endOffset="6356"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Monitoring.java"
            line="188"
            column="21"
            startOffset="8654"
            endLine="188"
            endColumn="72"
            endOffset="8705"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Monitoring.java"
            line="199"
            column="17"
            startOffset="8963"
            endLine="199"
            endColumn="68"
            endOffset="9014"/>
    </incident>

    <incident
        id="Recycle"
        severity="warning"
        message="This `Cursor` should be freed up after use with `#close()`">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="71"
            column="33"
            startOffset="3130"
            endLine="71"
            endColumn="41"
            endOffset="3138"/>
    </incident>

    <incident
        id="Recycle"
        severity="warning"
        message="This `Cursor` should be freed up after use with `#close()`">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="189"
            column="33"
            startOffset="8038"
            endLine="189"
            endColumn="41"
            endOffset="8046"/>
    </incident>

    <incident
        id="Recycle"
        severity="warning"
        message="This `Cursor` should be freed up after use with `#close()`">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="200"
            column="33"
            startOffset="8519"
            endLine="200"
            endColumn="41"
            endOffset="8527"/>
    </incident>

    <incident
        id="Recycle"
        severity="warning"
        message="This `Cursor` should be freed up after use with `#close()`">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
            line="285"
            column="34"
            startOffset="11704"
            endLine="285"
            endColumn="42"
            endOffset="11712"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_alignParentLeft`">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="97"
            column="17"
            startOffset="4288"
            endLine="97"
            endColumn="54"
            endOffset="4325"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_alignParentRight`">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="208"
            column="21"
            startOffset="9920"
            endLine="208"
            endColumn="59"
            endOffset="9958"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_below`">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="287"
            column="17"
            startOffset="11863"
            endLine="287"
            endColumn="48"
            endOffset="11894"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/Dialfunction.java"
            line="140"
            column="39"
            startOffset="5460"
            endLine="140"
            endColumn="85"
            endOffset="5506"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="305"
            column="13"
            startOffset="13251"
            endLine="305"
            endColumn="40"
            endOffset="13278"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="388"
            column="21"
            startOffset="16970"
            endLine="388"
            endColumn="48"
            endOffset="16997"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is never &lt; 24">
        <fix-data conditional="false"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="561"
            column="67"
            startOffset="25199"
            endLine="561"
            endColumn="93"
            endOffset="25225"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="600"
            column="13"
            startOffset="26583"
            endLine="600"
            endColumn="40"
            endOffset="26610"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="1297"
            column="13"
            startOffset="60040"
            endLine="1297"
            endColumn="40"
            endOffset="60067"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is never &lt; 24">
        <fix-data conditional="false"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/util/NotificationUtils.java"
            line="146"
            column="13"
            startOffset="5793"
            endLine="146"
            endColumn="70"
            endOffset="5850"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/PushService.java"
            line="142"
            column="13"
            startOffset="5604"
            endLine="142"
            endColumn="59"
            endOffset="5650"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/Smsend.java"
            line="194"
            column="17"
            startOffset="6739"
            endLine="194"
            endColumn="63"
            endOffset="6785"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="This folder configuration (`v24`) is unnecessary; `minSdkVersion` is 24. Merge all the resources in this folder into `drawable`.">
        <fix-data file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-v24" folderName="drawable" requiresApi="24"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-v24"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="This `AsyncTask` class should be static or leaks might occur (com.appystore.mrecharge.activity.MainActivity.Checkstatus)">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="833"
            column="19"
            startOffset="36916"
            endLine="833"
            endColumn="30"
            endOffset="36927"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1089 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="20"
            column="31"
            startOffset="2493"
            endLine="20"
            endColumn="1120"
            endOffset="3582"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (882 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="40"
            column="31"
            startOffset="9051"
            endLine="40"
            endColumn="913"
            endOffset="9933"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (943 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="44"
            column="31"
            startOffset="10356"
            endLine="44"
            endColumn="974"
            endOffset="11299"/>
    </incident>

    <incident
        id="UseValueOf"
        severity="warning"
        message="Use `Integer.valueOf(matcher.group().toString())` instead">
        <fix-replace
            description="Replace with valueOf()"
            family="Replace with valueOf()"
            robot="true"
            independent="true"
            oldPattern="(new.+)\("
            replacement="Integer.valueOf"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Tes.java"
            line="91"
            column="36"
            startOffset="4157"
            endLine="91"
            endColumn="75"
            endOffset="4196"/>
    </incident>

    <incident
        id="UseValueOf"
        severity="warning"
        message="Use `Integer.valueOf(matcher.group().toString())` instead">
        <fix-replace
            description="Replace with valueOf()"
            family="Replace with valueOf()"
            robot="true"
            independent="true"
            oldPattern="(new.+)\("
            replacement="Integer.valueOf"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Tes.java"
            line="216"
            column="36"
            startOffset="9752"
            endLine="216"
            endColumn="75"
            endOffset="9791"/>
    </incident>

    <incident
        id="UseValueOf"
        severity="warning"
        message="Use `Integer.valueOf(matcher.group().toString())` instead">
        <fix-replace
            description="Replace with valueOf()"
            family="Replace with valueOf()"
            robot="true"
            independent="true"
            oldPattern="(new.+)\("
            replacement="Integer.valueOf"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/Tes.java"
            line="240"
            column="36"
            startOffset="10810"
            endLine="240"
            endColumn="75"
            endOffset="10849"/>
    </incident>

    <incident
        id="UseValueOf"
        severity="warning"
        message="Use `Integer.valueOf(matcher.group().toString())` instead">
        <fix-replace
            description="Replace with valueOf()"
            family="Replace with valueOf()"
            robot="true"
            independent="true"
            oldPattern="(new.+)\("
            replacement="Integer.valueOf"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="479"
            column="36"
            startOffset="17804"
            endLine="479"
            endColumn="75"
            endOffset="17843"/>
    </incident>

    <incident
        id="UseValueOf"
        severity="warning"
        message="Use `Integer.valueOf(matcher.group().toString())` instead">
        <fix-replace
            description="Replace with valueOf()"
            family="Replace with valueOf()"
            robot="true"
            independent="true"
            oldPattern="(new.+)\("
            replacement="Integer.valueOf"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/USSDService.java"
            line="502"
            column="36"
            startOffset="18834"
            endLine="502"
            endColumn="75"
            endOffset="18873"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="8"
            column="6"
            startOffset="368"
            endLine="8"
            endColumn="18"
            endOffset="380"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="10"
            column="6"
            startOffset="375"
            endLine="10"
            endColumn="18"
            endOffset="387"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="8"
            column="6"
            startOffset="368"
            endLine="8"
            endColumn="18"
            endOffset="380"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#ff1b1a1a` with a theme that also paints a background (inferred theme is `@style/Theme.AppyStoreMRecharge`)">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_todo.xml"
            line="5"
            column="5"
            startOffset="188"
            endLine="5"
            endColumn="35"
            endOffset="218"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#ff1b1a1a` with a theme that also paints a background (inferred theme is `@style/Theme.AppyStoreMRecharge`)">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/listmain.xml"
            line="3"
            column="5"
            startOffset="120"
            endLine="3"
            endColumn="35"
            endOffset="150"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="89"
            column="14"
            startOffset="3906"
            endLine="89"
            endColumn="26"
            endOffset="3918"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/appystoremrecharge.png` in densityless folder">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/appystoremrecharge.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/bg.png` in densityless folder">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_stat_name.png` in densityless folder">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_stat_name.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/toggle_off.png` in densityless folder">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/toggle_off.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/toggle_on.png` in densityless folder">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/toggle_on.png"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="228"
            column="18"
            startOffset="9118"
            endLine="228"
            endColumn="26"
            endOffset="9126"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="147"
            column="18"
            startOffset="5747"
            endLine="147"
            endColumn="26"
            endOffset="5755"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="184"
            column="18"
            startOffset="7368"
            endLine="184"
            endColumn="26"
            endOffset="7376"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="221"
            column="18"
            startOffset="8989"
            endLine="221"
            endColumn="26"
            endOffset="8997"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="242"
            column="14"
            startOffset="9939"
            endLine="242"
            endColumn="22"
            endOffset="9947"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="262"
            column="14"
            startOffset="10805"
            endLine="262"
            endColumn="22"
            endOffset="10813"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="228"
            column="18"
            startOffset="9118"
            endLine="228"
            endColumn="26"
            endOffset="9126"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="248"
            column="22"
            startOffset="10111"
            endLine="248"
            endColumn="30"
            endOffset="10119"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="107"
            column="18"
            startOffset="4812"
            endLine="107"
            endColumn="26"
            endOffset="4820"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="127"
            column="18"
            startOffset="5822"
            endLine="127"
            endColumn="26"
            endOffset="5830"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="147"
            column="18"
            startOffset="6845"
            endLine="147"
            endColumn="26"
            endOffset="6853"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="167"
            column="18"
            startOffset="7870"
            endLine="167"
            endColumn="26"
            endOffset="7878"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="187"
            column="18"
            startOffset="8884"
            endLine="187"
            endColumn="26"
            endOffset="8892"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="147"
            column="18"
            startOffset="5747"
            endLine="147"
            endColumn="26"
            endOffset="5755"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="184"
            column="18"
            startOffset="7368"
            endLine="184"
            endColumn="26"
            endOffset="7376"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="221"
            column="18"
            startOffset="8989"
            endLine="221"
            endColumn="26"
            endOffset="8997"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="242"
            column="14"
            startOffset="9939"
            endLine="242"
            endColumn="22"
            endOffset="9947"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="262"
            column="14"
            startOffset="10805"
            endLine="262"
            endColumn="22"
            endOffset="10813"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="81"
            column="22"
            startOffset="3332"
            endLine="81"
            endColumn="30"
            endOffset="3340"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="123"
            column="22"
            startOffset="5192"
            endLine="123"
            endColumn="30"
            endOffset="5200"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for tools-desugar_jdk_libs"
            robot="true">
            <fix-replace
                description="Replace with desugar_jdk_libsVersion = &quot;2.1.3&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="desugar_jdk_libsVersion = &quot;2.1.3&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with tools-desugar_jdk_libs = { module = &quot;com.android.tools:desugar_jdk_libs&quot;, version.ref = &quot;desugar_jdk_libsVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="tools-desugar_jdk_libs = { module = &quot;com.android.tools:desugar_jdk_libs&quot;, version.ref = &quot;desugar_jdk_libsVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1838"
                    endOffset="1838"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.tools.desugar.jdk.libs"
                robot="true"
                replacement="libs.tools.desugar.jdk.libs"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="805"
                    endOffset="847"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="34"
            column="27"
            startOffset="805"
            endLine="34"
            endColumn="69"
            endOffset="847"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (com.squareup.okhttp3:okhttp is already available as `okhttp`, but using version 4.12.0 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for okhttp3-okhttp-v4110"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with squareupOkhttp = &quot;4.11.0&quot;..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="squareupOkhttp = &quot;4.11.0&quot;&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="322"
                        endOffset="322"/>
                </fix-replace>
                <fix-replace
                    description="Replace with okhttp3-okhttp-v4110 = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;squareupOkhttp&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="okhttp3-okhttp-v4110 = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;squareupOkhttp&quot; }&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="1093"
                        endOffset="1093"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.okhttp3.okhttp.v4110"
                    robot="true"
                    independent="true"
                    replacement="libs.okhttp3.okhttp.v4110"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle"
                        startOffset="1237"
                        endOffset="1273"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `okhttp` (version 4.12.0)"
                replacement="libs.okhttp"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1237"
                    endOffset="1273"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle"
            line="48"
            column="20"
            startOffset="1237"
            endLine="48"
            endColumn="56"
            endOffset="1273"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (com.squareup.okhttp3:logging-interceptor is already available as `logging-interceptor`, but using version 4.12.0 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for okhttp3-logging-interceptor-v4110"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with loggingInterceptorVersion = &quot;4.11.0&quot;..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="loggingInterceptorVersion = &quot;4.11.0&quot;&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="109"
                        endOffset="109"/>
                </fix-replace>
                <fix-replace
                    description="Replace with okhttp3-logging-interceptor-v4110 = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptorVersion&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="okhttp3-logging-interceptor-v4110 = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptorVersion&quot; }&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="1093"
                        endOffset="1093"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.okhttp3.logging.interceptor.v4110"
                    robot="true"
                    independent="true"
                    replacement="libs.okhttp3.logging.interceptor.v4110"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle"
                        startOffset="1293"
                        endOffset="1342"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `logging-interceptor` (version 4.12.0)"
                replacement="libs.logging.interceptor"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1293"
                    endOffset="1342"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle"
            line="49"
            column="20"
            startOffset="1293"
            endLine="49"
            endColumn="69"
            endOffset="1342"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-annotation"
            robot="true">
            <fix-replace
                description="Replace with annotationVersion = &quot;1.7.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="annotationVersion = &quot;1.7.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-annotation = { module = &quot;androidx.annotation:annotation&quot;, version.ref = &quot;annotationVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-annotation = { module = &quot;androidx.annotation:annotation&quot;, version.ref = &quot;annotationVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="431"
                    endOffset="431"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.annotation"
                robot="true"
                replacement="libs.androidx.annotation"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1410"
                    endOffset="1448"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="52"
            column="20"
            startOffset="1410"
            endLine="52"
            endColumn="58"
            endOffset="1448"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for facebook-stetho"
            robot="true">
            <fix-replace
                description="Replace with stethoVersion = &quot;1.6.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="stethoVersion = &quot;1.6.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="322"
                    endOffset="322"/>
            </fix-replace>
            <fix-replace
                description="Replace with facebook-stetho = { module = &quot;com.facebook.stetho:stetho&quot;, version.ref = &quot;stethoVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="facebook-stetho = { module = &quot;com.facebook.stetho:stetho&quot;, version.ref = &quot;stethoVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="431"
                    endOffset="431"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.facebook.stetho"
                robot="true"
                replacement="libs.facebook.stetho"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1987"
                    endOffset="2021"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="71"
            column="25"
            startOffset="1987"
            endLine="71"
            endColumn="59"
            endOffset="2021"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for facebook-stetho-okhttp3"
            robot="true">
            <fix-replace
                description="Replace with stethoOkhttp3Version = &quot;1.6.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="stethoOkhttp3Version = &quot;1.6.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="322"
                    endOffset="322"/>
            </fix-replace>
            <fix-replace
                description="Replace with facebook-stetho-okhttp3 = { module = &quot;com.facebook.stetho:stetho-okhttp3&quot;, version.ref = &quot;stethoOkhttp3Version&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="facebook-stetho-okhttp3 = { module = &quot;com.facebook.stetho:stetho-okhttp3&quot;, version.ref = &quot;stethoOkhttp3Version&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="431"
                    endOffset="431"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.facebook.stetho.okhttp3"
                robot="true"
                replacement="libs.facebook.stetho.okhttp3"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2046"
                    endOffset="2088"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="72"
            column="25"
            startOffset="2046"
            endLine="72"
            endColumn="67"
            endOffset="2088"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="42"
            column="14"
            startOffset="1621"
            endLine="42"
            endColumn="23"
            endOffset="1630"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="75"
            column="14"
            startOffset="2915"
            endLine="75"
            endColumn="23"
            endOffset="2924"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="107"
            column="14"
            startOffset="4180"
            endLine="107"
            endColumn="23"
            endOffset="4189"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="139"
            column="14"
            startOffset="5436"
            endLine="139"
            endColumn="23"
            endOffset="5445"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="176"
            column="18"
            startOffset="6763"
            endLine="176"
            endColumn="27"
            endOffset="6772"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="22"
            column="14"
            startOffset="945"
            endLine="22"
            endColumn="25"
            endOffset="956"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="43"
            column="14"
            startOffset="1864"
            endLine="43"
            endColumn="25"
            endOffset="1875"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="64"
            column="14"
            startOffset="2801"
            endLine="64"
            endColumn="25"
            endOffset="2812"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="28"
            column="14"
            startOffset="1035"
            endLine="28"
            endColumn="25"
            endOffset="1046"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="54"
            column="14"
            startOffset="2048"
            endLine="54"
            endColumn="25"
            endOffset="2059"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="79"
            column="14"
            startOffset="3020"
            endLine="79"
            endColumn="25"
            endOffset="3031"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="23"
            column="14"
            startOffset="981"
            endLine="23"
            endColumn="25"
            endOffset="992"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="45"
            column="14"
            startOffset="1933"
            endLine="45"
            endColumn="25"
            endOffset="1944"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="67"
            column="14"
            startOffset="2901"
            endLine="67"
            endColumn="25"
            endOffset="2912"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="93"
            column="14"
            startOffset="4047"
            endLine="93"
            endColumn="23"
            endOffset="4056"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="271"
            column="28"
            startOffset="11694"
            endLine="271"
            endColumn="86"
            endOffset="11752"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/activity/MainActivity.java"
            line="271"
            column="28"
            startOffset="11694"
            endLine="271"
            endColumn="41"
            endOffset="11707"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/RecyclerviewItemAdapter.java"
            line="47"
            column="41"
            startOffset="1484"
            endLine="47"
            endColumn="53"
            endOffset="1496"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/RecyclerviewItemAdapter.java"
            line="51"
            column="41"
            startOffset="1683"
            endLine="51"
            endColumn="47"
            endOffset="1689"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/RecyclerviewItemAdapter.java"
            line="55"
            column="41"
            startOffset="1884"
            endLine="55"
            endColumn="53"
            endOffset="1896"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/RecyclerviewItemAdapter.java"
            line="59"
            column="41"
            startOffset="2086"
            endLine="59"
            endColumn="49"
            endOffset="2094"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/RecyclerviewItemAdapter.java"
            line="67"
            column="38"
            startOffset="2325"
            endLine="67"
            endColumn="60"
            endOffset="2347"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="52"
            column="17"
            startOffset="2010"
            endLine="52"
            endColumn="36"
            endOffset="2029"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Banking&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="85"
            column="17"
            startOffset="3313"
            endLine="85"
            endColumn="39"
            endOffset="3335"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="117"
            column="17"
            startOffset="4573"
            endLine="117"
            endColumn="40"
            endOffset="4596"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Monitor&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="149"
            column="17"
            startOffset="5833"
            endLine="149"
            endColumn="39"
            endOffset="5855"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Automatic Mobile Recharge System&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="189"
            column="21"
            startOffset="7358"
            endLine="189"
            endColumn="68"
            endOffset="7405"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Device ID: **********&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="203"
            column="21"
            startOffset="8064"
            endLine="203"
            endColumn="57"
            endOffset="8100"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;License Information&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="223"
            column="21"
            startOffset="8925"
            endLine="223"
            endColumn="55"
            endOffset="8959"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;License Key eg: xxxxx-xxxxx-xxxxx-xxxxx&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="236"
            column="21"
            startOffset="9555"
            endLine="236"
            endColumn="75"
            endOffset="9609"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;PIN&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="256"
            column="25"
            startOffset="10565"
            endLine="256"
            endColumn="43"
            endOffset="10583"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;1234&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="259"
            column="25"
            startOffset="10714"
            endLine="259"
            endColumn="44"
            endOffset="10733"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;V.1&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="276"
            column="29"
            startOffset="11551"
            endLine="276"
            endColumn="47"
            endOffset="11569"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;V.2&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="284"
            column="29"
            startOffset="11931"
            endLine="284"
            endColumn="47"
            endOffset="11949"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Network Settings&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="315"
            column="21"
            startOffset="13206"
            endLine="315"
            endColumn="52"
            endOffset="13237"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SIM A:&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="333"
            column="25"
            startOffset="14025"
            endLine="333"
            endColumn="46"
            endOffset="14046"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SIM B:&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="359"
            column="25"
            startOffset="15206"
            endLine="359"
            endColumn="46"
            endOffset="15227"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;System Settings&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="388"
            column="21"
            startOffset="16439"
            endLine="388"
            endColumn="51"
            endOffset="16469"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SMS Service&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="407"
            column="25"
            startOffset="17289"
            endLine="407"
            endColumn="51"
            endOffset="17315"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Accessibility&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="436"
            column="25"
            startOffset="18642"
            endLine="436"
            endColumn="53"
            endOffset="18670"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Power Optimize&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="465"
            column="25"
            startOffset="19991"
            endLine="465"
            endColumn="54"
            endOffset="20020"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Find Page&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="493"
            column="25"
            startOffset="21285"
            endLine="493"
            endColumn="49"
            endOffset="21309"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="6"
            column="9"
            startOffset="206"
            endLine="6"
            endColumn="29"
            endOffset="226"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;M Banking&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="10"
            column="9"
            startOffset="340"
            endLine="10"
            endColumn="34"
            endOffset="365"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="14"
            column="9"
            startOffset="473"
            endLine="14"
            endColumn="33"
            endOffset="497"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Monitor&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="18"
            column="9"
            startOffset="610"
            endLine="18"
            endColumn="32"
            endOffset="633"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="34"
            column="17"
            startOffset="1515"
            endLine="34"
            endColumn="36"
            endOffset="1534"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;M Banking&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="55"
            column="17"
            startOffset="2443"
            endLine="55"
            endColumn="41"
            endOffset="2467"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="76"
            column="17"
            startOffset="3374"
            endLine="76"
            endColumn="40"
            endOffset="3397"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;GP&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="106"
            column="21"
            startOffset="4774"
            endLine="106"
            endColumn="38"
            endOffset="4791"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter USSD&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="116"
            column="21"
            startOffset="5259"
            endLine="116"
            endColumn="46"
            endOffset="5284"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;ROBI&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="126"
            column="21"
            startOffset="5782"
            endLine="126"
            endColumn="40"
            endOffset="5801"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter USSD&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="136"
            column="21"
            startOffset="6271"
            endLine="136"
            endColumn="46"
            endOffset="6296"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;AIRTEL&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="146"
            column="21"
            startOffset="6803"
            endLine="146"
            endColumn="42"
            endOffset="6824"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter USSD&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="156"
            column="21"
            startOffset="7292"
            endLine="156"
            endColumn="46"
            endOffset="7317"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;BANGLALINK&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="166"
            column="21"
            startOffset="7824"
            endLine="166"
            endColumn="46"
            endOffset="7849"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter USSD&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="176"
            column="21"
            startOffset="8317"
            endLine="176"
            endColumn="46"
            endOffset="8342"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TELETALK&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="186"
            column="21"
            startOffset="8840"
            endLine="186"
            endColumn="44"
            endOffset="8863"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter USSD&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="196"
            column="21"
            startOffset="9331"
            endLine="196"
            endColumn="46"
            endOffset="9356"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Save&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="207"
            column="21"
            startOffset="9879"
            endLine="207"
            endColumn="40"
            endOffset="9898"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot; Ok &quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/custom_dialog.xml"
            line="11"
            column="9"
            startOffset="428"
            endLine="11"
            endColumn="28"
            endOffset="447"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="40"
            column="17"
            startOffset="1528"
            endLine="40"
            endColumn="36"
            endOffset="1547"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;M Banking&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="66"
            column="17"
            startOffset="2550"
            endLine="66"
            endColumn="41"
            endOffset="2574"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="91"
            column="17"
            startOffset="3517"
            endLine="91"
            endColumn="40"
            endOffset="3540"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Forward Rquest to sms&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="104"
            column="9"
            startOffset="4010"
            endLine="104"
            endColumn="45"
            endOffset="4046"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Bkash&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="127"
            column="17"
            startOffset="4898"
            endLine="127"
            endColumn="37"
            endOffset="4918"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter number here&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="153"
            column="21"
            startOffset="6046"
            endLine="153"
            endColumn="53"
            endOffset="6078"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Rocket&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="164"
            column="17"
            startOffset="6518"
            endLine="164"
            endColumn="38"
            endOffset="6539"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter number here&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="190"
            column="21"
            startOffset="7668"
            endLine="190"
            endColumn="53"
            endOffset="7700"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Nogad&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="201"
            column="17"
            startOffset="8140"
            endLine="201"
            endColumn="37"
            endOffset="8160"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter number here&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="227"
            column="21"
            startOffset="9288"
            endLine="227"
            endColumn="53"
            endOffset="9320"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Request Every sec&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="238"
            column="17"
            startOffset="9760"
            endLine="238"
            endColumn="49"
            endOffset="9792"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0 sec&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="248"
            column="17"
            startOffset="10213"
            endLine="248"
            endColumn="37"
            endOffset="10233"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Maximum Try&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="258"
            column="17"
            startOffset="10632"
            endLine="258"
            endColumn="43"
            endOffset="10658"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="268"
            column="17"
            startOffset="11079"
            endLine="268"
            endColumn="33"
            endOffset="11095"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Dial function&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="279"
            column="17"
            startOffset="11533"
            endLine="279"
            endColumn="45"
            endOffset="11561"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Save&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="299"
            column="17"
            startOffset="12404"
            endLine="299"
            endColumn="36"
            endOffset="12423"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;License Activation&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="38"
            column="21"
            startOffset="1442"
            endLine="38"
            endColumn="54"
            endOffset="1475"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter your license key and PIN to activate&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="48"
            column="21"
            startOffset="1899"
            endLine="48"
            endColumn="78"
            endOffset="1956"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;License Key&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="74"
            column="25"
            startOffset="3002"
            endLine="74"
            endColumn="51"
            endOffset="3028"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter your license key&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="85"
            column="25"
            startOffset="3540"
            endLine="85"
            endColumn="62"
            endOffset="3577"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;PIN&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="116"
            column="25"
            startOffset="4870"
            endLine="116"
            endColumn="43"
            endOffset="4888"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter your PIN&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="127"
            column="25"
            startOffset="5393"
            endLine="127"
            endColumn="54"
            endOffset="5422"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Activate License&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="145"
            column="17"
            startOffset="6148"
            endLine="145"
            endColumn="48"
            endOffset="6179"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Please enter your license key and PIN to activate&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="160"
            column="17"
            startOffset="6807"
            endLine="160"
            endColumn="81"
            endOffset="6871"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🌐&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="202"
            column="33"
            startOffset="8795"
            endLine="202"
            endColumn="50"
            endOffset="8812"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Domain Access&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="209"
            column="33"
            startOffset="9150"
            endLine="209"
            endColumn="61"
            endOffset="9178"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Your domain: Loading...&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="221"
            column="29"
            startOffset="9732"
            endLine="221"
            endColumn="67"
            endOffset="9770"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🎛️ Open Dashboard&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="231"
            column="29"
            startOffset="10261"
            endLine="231"
            endColumn="62"
            endOffset="10294"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;💡 View your license details and domain information&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="244"
            column="29"
            startOffset="10986"
            endLine="244"
            endColumn="95"
            endOffset="11052"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📅&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="288"
            column="33"
            startOffset="12954"
            endLine="288"
            endColumn="50"
            endOffset="12971"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;License Status&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="295"
            column="33"
            startOffset="13309"
            endLine="295"
            endColumn="62"
            endOffset="13338"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;License expires: Loading...&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="307"
            column="29"
            startOffset="13892"
            endLine="307"
            endColumn="71"
            endOffset="13934"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Time remaining: Calculating...&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="317"
            column="29"
            startOffset="14431"
            endLine="317"
            endColumn="74"
            endOffset="14476"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;💡 You will receive warnings 2 days before expiration&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="337"
            column="29"
            startOffset="15532"
            endLine="337"
            endColumn="97"
            endOffset="15600"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;👨‍💻&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="374"
            column="17"
            startOffset="16826"
            endLine="374"
            endColumn="37"
            endOffset="16846"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Md Sadrul Hasan Dider&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="381"
            column="17"
            startOffset="17088"
            endLine="381"
            endColumn="53"
            endOffset="17124"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;© 2025 License Activation System&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml"
            line="392"
            column="13"
            startOffset="17472"
            endLine="392"
            endColumn="60"
            endOffset="17519"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Mobile Banking&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_main.xml"
            line="6"
            column="9"
            startOffset="250"
            endLine="6"
            endColumn="39"
            endOffset="280"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Forward&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_main.xml"
            line="11"
            column="9"
            startOffset="411"
            endLine="11"
            endColumn="32"
            endOffset="434"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="35"
            column="17"
            startOffset="1548"
            endLine="35"
            endColumn="36"
            endOffset="1567"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;M Banking&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="57"
            column="17"
            startOffset="2508"
            endLine="57"
            endColumn="41"
            endOffset="2532"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="79"
            column="17"
            startOffset="3471"
            endLine="79"
            endColumn="40"
            endOffset="3494"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Automatic Mobile recharge system&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="107"
            column="17"
            startOffset="4745"
            endLine="107"
            endColumn="64"
            endOffset="4792"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Bkash&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="122"
            column="21"
            startOffset="5536"
            endLine="122"
            endColumn="41"
            endOffset="5556"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Rocket&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="149"
            column="21"
            startOffset="6855"
            endLine="149"
            endColumn="42"
            endOffset="6876"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Nogad&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="176"
            column="21"
            startOffset="8176"
            endLine="176"
            endColumn="41"
            endOffset="8196"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Upay&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="203"
            column="21"
            startOffset="9495"
            endLine="203"
            endColumn="40"
            endOffset="9514"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Bill pay&quot;, should use `@string` resource">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="230"
            column="21"
            startOffset="10812"
            endLine="230"
            endColumn="44"
            endOffset="10835"/>
    </incident>

</incidents>
