package com.appystore.mrecharge.app;


public class Config {
    // ========================================
    // NOTIFICATION CONFIGURATION
    // ========================================
    public static final int NOTIFICATION_ID = 100;
    public static final int NOTIFICATION_ID_BIG_IMAGE = 101;
    public static final String PUSH_NOTIFICATION = "pushNotification";
    public static final String REGISTRATION_COMPLETE = "registrationComplete";
    public static final String SHARED_PREF = "ah_firebase";
    public static final String TOPIC_GLOBAL = "global";

    // ========================================
    // API CONFIGURATION
    // ========================================

    /**
     * Default API base URL for license authentication
     * This is the primary endpoint for device authentication and license validation
     */
    //public static final String DEFAULT_LICENSE_API_URL = "http://192.168.0.106/AppyStoreMRecharge/mrecharge_simsupport_telecom/License_of_Tel_beautiful_design/admin/api/device_auth.php";
    public static final String DEFAULT_LICENSE_API_URL = "https://telelicense.diderappstore.top/admin/api/device_auth.php";

    /**
     * Default base domain for API services
     * Used when no custom domain is configured via license validation
     */
   // public static final String DEFAULT_API_BASE_DOMAIN = "192.168.0.106/ashiktelecom";
    public static final String DEFAULT_API_BASE_DOMAIN = "appystoretelecom.diderappstore.top";

    /**
     * Default protocol for API calls
     */
    public static final String DEFAULT_API_PROTOCOL = "http";

    // ========================================
    // API ENDPOINT PATHS
    // ========================================

    /**
     * Device registration endpoint path
     * Used by Devicer.java interface
     */
    public static final String ENDPOINT_DEVICE_REGISTRATION = "index.php/Modemcon/device";

    /**
     * Service request endpoint path
     * Used by WeatherAPIs.java interface for recharge requests
     */
    public static final String ENDPOINT_SERVICE_REQUEST = "index.php/Modemcon/request";

    /**
     * Response update endpoint path
     * Used by Responseupdate.java interface for updating response status
     */
    public static final String ENDPOINT_RESPONSE_UPDATE = "index.php/Modemcon/updateres";

    /**
     * Message update endpoint path
     * Used by Msgupdate.java interface for message updates
     */
    public static final String ENDPOINT_MESSAGE_UPDATE = "index.php/Modemcon/update";

    // ========================================
    // API CONFIGURATION KEYS
    // ========================================

    /**
     * SharedPreferences key for storing custom API URL
     */
    public static final String PREF_KEY_API_URL = "api_url";

    /**
     * SharedPreferences key for storing API domain
     */
    public static final String PREF_KEY_API_DOMAIN = "url";

    /**
     * SharedPreferences key for storing API protocol (http/https)
     */
    public static final String PREF_KEY_API_PROTOCOL = "sec";

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Constructs the full API URL from protocol and domain
     * @param protocol The protocol (http/https)
     * @param domain The domain with path
     * @return Complete base URL for API calls
     */
    public static String buildApiBaseUrl(String protocol, String domain) {
        if (protocol == null || protocol.isEmpty()) {
            protocol = DEFAULT_API_PROTOCOL;
        }
        if (domain == null || domain.isEmpty()) {
            domain = DEFAULT_API_BASE_DOMAIN;
        }

        // Remove /mitload if present (legacy path correction)
        if (domain.contains("/mitload")) {
            domain = domain.replace("/mitload", "");
        }

        return protocol + "://" + domain;
    }

    /**
     * Gets the default license API URL
     * @return The default license authentication API URL
     */
    public static String getDefaultLicenseApiUrl() {
        return DEFAULT_LICENSE_API_URL;
    }
}
