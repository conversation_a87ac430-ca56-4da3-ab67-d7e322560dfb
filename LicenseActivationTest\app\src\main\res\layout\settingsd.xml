<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/activity_main"
    android:background="@drawable/bgg"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        android:id="@+id/btnSpeakContainer"
        android:paddingTop="0dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:id="@+id/home"
            android:background="@drawable/rightorder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <ImageButton
                android:background="@null"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:src="@drawable/ic_home_black_24dp"
                android:scaleType="fitCenter"
                app:tint="#E91E63" />
            <TextView
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:paddingLeft="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Home"/>
        </LinearLayout>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:id="@+id/bank"
            android:background="@drawable/select"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <ImageButton
                android:background="@null"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:src="@drawable/ic_settings_cell_black_24dp"
                app:tint="#E91E63"
                android:scaleType="fitCenter"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:paddingLeft="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="M Banking"/>
        </LinearLayout>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:id="@+id/sms"
            android:background="@drawable/rightorder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <ImageButton
                android:background="@null"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                app:tint="#E91E63"
                android:src="@drawable/ic_settings_black_24dp"
                android:scaleType="fitCenter"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:paddingLeft="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Settings"/>
        </LinearLayout>
    </LinearLayout>
    <ScrollView
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:paddingRight="@dimen/activity_horizontal_margin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="1dp"
        android:layout_marginRight="1dp">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <ImageView
                android:id="@+id/imageView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                app:srcCompat="@mipmap/ic_launcher"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="#ffffffff"
                android:layout_gravity="center_horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Automatic Mobile recharge system"/>
            <RelativeLayout
                android:layout_gravity="center"
                android:background="@drawable/rounded_corner"
                android:padding="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp">
                <TextView
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="@color/white"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:text="Bkash"/>
                <ToggleButton
                    android:id="@+id/bkash"
                    android:background="@drawable/toggle_selector"
                    android:layout_width="35dp"
                    android:layout_height="20dp"
                    android:checked="false"
                    android:textOn=""
                    android:textOff=""
                    android:text=""
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_gravity="center"
                android:background="@drawable/rounded_corner"
                android:padding="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp">
                <TextView
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="@color/white"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:text="Rocket"/>
                <ToggleButton
                    android:id="@+id/rocket"
                    android:background="@drawable/toggle_selector"
                    android:layout_width="35dp"
                    android:layout_height="20dp"
                    android:checked="false"
                    android:textOn=""
                    android:textOff=""
                    android:text=""
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_gravity="center"
                android:background="@drawable/rounded_corner"
                android:padding="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp">
                <TextView
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="@color/white"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:text="Nogad"/>
                <ToggleButton
                    android:id="@+id/nogad"
                    android:background="@drawable/toggle_selector"
                    android:layout_width="35dp"
                    android:layout_height="20dp"
                    android:checked="false"
                    android:textOn=""
                    android:textOff=""
                    android:text=""
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_gravity="center"
                android:background="@drawable/rounded_corner"
                android:padding="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp">
                <TextView
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="@color/white"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:text="Upay"/>
                <ToggleButton
                    android:id="@+id/upay"
                    android:background="@drawable/toggle_selector"
                    android:layout_width="35dp"
                    android:layout_height="20dp"
                    android:checked="false"
                    android:textOn=""
                    android:textOff=""
                    android:text=""
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_gravity="center"
                android:background="@drawable/rounded_corner"
                android:padding="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp">
                <TextView
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="@color/white"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:text="Bill pay"/>
                <ToggleButton
                    android:id="@+id/bill"
                    android:background="@drawable/toggle_selector"
                    android:layout_width="35dp"
                    android:layout_height="20dp"
                    android:checked="false"
                    android:textOn=""
                    android:textOff=""
                    android:text=""
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"/>
            </RelativeLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>