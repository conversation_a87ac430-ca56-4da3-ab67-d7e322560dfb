<libraries>
  <library
      name="com.google.firebase:firebase-auth:23.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\4877ba713b6c77cf952e5907d0a020cb\transformed\firebase-auth-23.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth:23.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\4877ba713b6c77cf952e5907d0a020cb\transformed\firebase-auth-23.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\5ce42f50b8e9cd31ff8c0bdc3b462bc2\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\5ce42f50b8e9cd31ff8c0bdc3b462bc2\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\fee9c8056b141e461f4c334455e2f961\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\fee9c8056b141e461f4c334455e2f961\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\1735b8cdd7f63af0f6534fc72d24b693\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\1735b8cdd7f63af0f6534fc72d24b693\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\13262cafe34d6aedf2ac6cbd1965e39d\transformed\play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\13262cafe34d6aedf2ac6cbd1965e39d\transformed\play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck-interop:17.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\9bf93b9cac6011f3c994b93faf9d6659\transformed\firebase-appcheck-interop-17.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck-interop:17.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\9bf93b9cac6011f3c994b93faf9d6659\transformed\firebase-appcheck-interop-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\51f01dee9f3222424bc9040047ad476b\transformed\play-services-base-18.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\51f01dee9f3222424bc9040047ad476b\transformed\play-services-base-18.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.recaptcha:recaptcha:18.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\435d61ecbbe3481154f76d1f2bd8d528\transformed\recaptcha-18.6.1\jars\classes.jar"
      resolved="com.google.android.recaptcha:recaptcha:18.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\435d61ecbbe3481154f76d1f2bd8d528\transformed\recaptcha-18.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.play:integrity:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\54db06f05eb728b65b7e5a870e7d43a7\transformed\integrity-1.3.0\jars\classes.jar"
      resolved="com.google.android.play:integrity:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\54db06f05eb728b65b7e5a870e7d43a7\transformed\integrity-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\a1c4696eef97969debdbf3f3a05f23b0\transformed\firebase-auth-interop-20.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth-interop:20.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\a1c4696eef97969debdbf3f3a05f23b0\transformed\firebase-auth-interop-20.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:24.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\9b6d5929ca286945b5ef616f64e4ea48\transformed\firebase-messaging-24.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\501be6f5311fc7674eaab5b094c8d259\transformed\firebase-installations-17.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\501be6f5311fc7674eaab5b094c8d259\transformed\firebase-installations-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\52d3036040e45607a255b1d2de9f9442\transformed\firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\52d3036040e45607a255b1d2de9f9442\transformed\firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\4127f8ed72cf9bd55f5f68508aa84b29\transformed\firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\4127f8ed72cf9bd55f5f68508aa84b29\transformed\firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\a2ee6c6ec182e85729b567b0981fb038\transformed\firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\a2ee6c6ec182e85729b567b0981fb038\transformed\firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\07f678a0ca4907834db1d485ccc0617e\transformed\firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\07f678a0ca4907834db1d485ccc0617e\transformed\firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\2346c276e8123d58131e2bf72ee70526\transformed\work-runtime-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\c20dbc919d74ed6428f648e519a57160\transformed\browser-1.4.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\c20dbc919d74ed6428f648e519a57160\transformed\browser-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\3de2857b0c8dd5b4e9a8f2f96084b0a8\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\3de2857b0c8dd5b4e9a8f2f96084b0a8\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\90756dc49a096540e8b9231e401d97f7\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\90756dc49a096540e8b9231e401d97f7\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\06667f3fa1dc890c40efbde97ef664d4\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\06667f3fa1dc890c40efbde97ef664d4\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\afa0feb61b18c190372e1d1ca68ca434\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\afa0feb61b18c190372e1d1ca68ca434\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\67f0325ae99897ce7e858db23bf1f6c2\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\67f0325ae99897ce7e858db23bf1f6c2\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\ef4ba64fe7729dff8b35d76a934fdbc1\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\ef4ba64fe7729dff8b35d76a934fdbc1\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\83cdb3d9daf0ec551c8cf5239309d55f\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\83cdb3d9daf0ec551c8cf5239309d55f\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\48367474bb262b4205761c2a0a6e3334\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\48367474bb262b4205761c2a0a6e3334\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\ee4c4fee0a0322e0237c3e9a1d1c6afd\transformed\lifecycle-livedata-core-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\ee4c4fee0a0322e0237c3e9a1d1c6afd\transformed\lifecycle-livedata-core-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\1f176be870578decc9219b1cc9b386de\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\1f176be870578decc9219b1cc9b386de\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\edc2eafea2152fa8e2e007df799c11c7\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\edc2eafea2152fa8e2e007df799c11c7\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\6d4c02a0e1ad7d14785f0645c47f9a2a\transformed\lifecycle-livedata-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\6d4c02a0e1ad7d14785f0645c47f9a2a\transformed\lifecycle-livedata-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\686748d02647fed536cef30e3395bf5a\transformed\lifecycle-viewmodel-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\686748d02647fed536cef30e3395bf5a\transformed\lifecycle-viewmodel-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\b772de0d5b18781ef7747fab959dfe70\transformed\lifecycle-viewmodel-savedstate-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\b772de0d5b18781ef7747fab959dfe70\transformed\lifecycle-viewmodel-savedstate-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\c178e1e830cc6ee9939251b6a8f8a10e\transformed\core-ktx-1.13.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\c178e1e830cc6ee9939251b6a8f8a10e\transformed\core-ktx-1.13.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\4c9794a4376265066e2be6156b9509f4\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\4c9794a4376265066e2be6156b9509f4\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\38f00f77f8607acf63431dbc5af22904\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\38f00f77f8607acf63431dbc5af22904\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0\jars\classes.jar"
      resolved="androidx.core:core:1.13.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\534e5608d8b8e741317680d9a7d159af\transformed\core-1.13.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\96de77cc64e453abeea349ba9df99d0f\transformed\lifecycle-runtime-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\96de77cc64e453abeea349ba9df99d0f\transformed\lifecycle-runtime-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.2\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.2.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.7.3\7087d47913cfb0062c9909dacbfc78fe44c5ecff\kotlinx-coroutines-play-services-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\03a90bb523df8d6e6a35c54d5a439635\transformed\play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\03a90bb523df8d6e6a35c54d5a439635\transformed\play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\9fbd2091722dfad1fa1f949231f18076\transformed\firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\9fbd2091722dfad1fa1f949231f18076\transformed\firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\5876d8f2a399c27f40b853a1096d16b3\transformed\play-services-basement-18.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\5876d8f2a399c27f40b853a1096d16b3\transformed\play-services-basement-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\54a7effaaa53a5f2c88bda5d988cb415\transformed\fragment-1.5.4\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\54a7effaaa53a5f2c88bda5d988cb415\transformed\fragment-1.5.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\c8f1fca503b9b271196e2856bff21d04\transformed\activity-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\c8f1fca503b9b271196e2856bff21d04\transformed\activity-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\7dad688352b682ab3d62550b14856592\transformed\constraintlayout-2.2.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\7dad688352b682ab3d62550b14856592\transformed\constraintlayout-2.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.9.0\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\converter-gson-2.9.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.9.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.9.0\d8fdfbd5da952141a665a403348b74538efc05ff\retrofit-2.9.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.9.0"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\924e837c2a3e6b6da67b31512a99dcbe\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\924e837c2a3e6b6da67b31512a99dcbe\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials-play-services-auth:1.2.0-rc01"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\196accc71b87476783482adbbea29fca\transformed\credentials-play-services-auth-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials:1.2.0-rc01@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\5846cdb8710318d8515685e81b5b854c\transformed\credentials-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials:1.2.0-rc01"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\5846cdb8710318d8515685e81b5b854c\transformed\credentials-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\153cdcb5508606641cd80dbb9abb169f\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\153cdcb5508606641cd80dbb9abb169f\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\a811dafc16cef210c58fe78a92081740\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\a811dafc16cef210c58fe78a92081740\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\b0ad4dda7ffaba2699f7ad30586bc8f5\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\b0ad4dda7ffaba2699f7ad30586bc8f5\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\9c0da2379390e7d82e9294d7df7ba6a7\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\9c0da2379390e7d82e9294d7df7ba6a7\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\fa1f719c297e50180d928c36fa59198f\transformed\firebase-datatransport-18.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\fa1f719c297e50180d928c36fa59198f\transformed\firebase-datatransport-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\77462b66581b121f1d14a06b75241abd\transformed\transport-backend-cct-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\77462b66581b121f1d14a06b75241abd\transformed\transport-backend-cct-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\f90cab5d95561b1aea014351424295f3\transformed\firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\f90cab5d95561b1aea014351424295f3\transformed\firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\22c52c145ca576338baa2e66d3cf2bdb\transformed\transport-runtime-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\22c52c145ca576338baa2e66d3cf2bdb\transformed\transport-runtime-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\2167549c83119b6b760e83b82b359165\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\2167549c83119b6b760e83b82b359165\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\084b5ff3f10ec778addc752911424a99\transformed\transport-api-3.1.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\084b5ff3f10ec778addc752911424a99\transformed\transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\5a9e819abfd9ef7d6de8af61e74918cc\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\5a9e819abfd9ef7d6de8af61e74918cc\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\d5c6badded153e2597a697f60a106e2b\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\d5c6badded153e2597a697f60a106e2b\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\4779fa57f616f834da3e2ac00dc78443\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\4779fa57f616f834da3e2ac00dc78443\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.volley:volley:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\5106677ecde16e20d8b640c1dcbce077\transformed\volley-1.2.1\jars\classes.jar"
      resolved="com.android.volley:volley:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\5106677ecde16e20d8b640c1dcbce077\transformed\volley-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.1\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\a99531b75361c023e4f63f1e26151e22\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\a99531b75361c023e4f63f1e26151e22\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\815086536b91b5875080f5053e0f2fad\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\815086536b91b5875080f5053e0f2fad\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.10\72812e8a368917ab5c0a5081b56915ffdfec93b7\kotlin-stdlib-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.9.10\dafaf2c27f27c09220cee312df10917d9a5d97ce\kotlin-stdlib-common-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\c872bece7adfd7694e2fcea621ac6e07\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\c872bece7adfd7694e2fcea621ac6e07\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.play:core-common:2.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\27726287844e0c57051862f89d0a94a5\transformed\core-common-2.0.3\jars\classes.jar"
      resolved="com.google.android.play:core-common:2.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\27726287844e0c57051862f89d0a94a5\transformed\core-common-2.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\95ad8fbd4cd52cf34bb2e45ff0110bd7\transformed\firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\95ad8fbd4cd52cf34bb2e45ff0110bd7\transformed\firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="com.google.code.gson:gson:2.8.5@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.8.5\f645ed69d595b24d4cf8b3fbb64cc505bede8829\gson-2.8.5.jar"
      resolved="com.google.code.gson:gson:2.8.5"/>
  <library
      name="com.google.android.gms:play-services-auth:20.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:20.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\9dd3b1cd2b8985ff1cd94450d80c19b1\transformed\play-services-auth-20.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\cb05db6c7d00f7e616c929dae3af37ed\transformed\play-services-fido-20.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\cb05db6c7d00f7e616c929dae3af37ed\transformed\play-services-fido-20.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\d67f98ee175f1221d2cda546f124a7a0\transformed\play-services-auth-base-18.0.4\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.4"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\d67f98ee175f1221d2cda546f124a7a0\transformed\play-services-auth-base-18.0.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\23e9870c42efcd1d130322c15f56ca23\transformed\play-services-cloud-messaging-17.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\23e9870c42efcd1d130322c15f56ca23\transformed\play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\9b5e8ea9d24a247b985fe564826eb664\transformed\room-ktx-2.5.0\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\9b5e8ea9d24a247b985fe564826eb664\transformed\room-ktx-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\4adadbfd556b2adf1819afb24edfc3e3\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\4adadbfd556b2adf1819afb24edfc3e3\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\aa38a909f3c22e0015a23f181d9b4b3b\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.14\transforms\aa38a909f3c22e0015a23f181d9b4b3b\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\aa38a909f3c22e0015a23f181d9b4b3b\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\5d919707ba2bdcdbdfaec4c1a54b3552\transformed\play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\5d919707ba2bdcdbdfaec4c1a54b3552\transformed\play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\ad60df95f30ca1010334e44a17134ef3\transformed\lifecycle-service-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\ad60df95f30ca1010334e44a17134ef3\transformed\lifecycle-service-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\a8f1fc009573b7426ba9e8f3843153ff\transformed\lifecycle-process-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\a8f1fc009573b7426ba9e8f3843153ff\transformed\lifecycle-process-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\2044b426090b747abc60e59a67ed720f\transformed\profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\0869a39c5f855dbe85e952a573886511\transformed\tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\0869a39c5f855dbe85e952a573886511\transformed\tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.1.1\f7ab6170b99b9421bd4942846426ff820b552f7d\constraintlayout-core-1.1.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.1.1"/>
  <library
      name="androidx.room:room-runtime:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\5b1dc2030c66ea128a57c372c477b987\transformed\room-runtime-2.5.0\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\5b1dc2030c66ea128a57c372c477b987\transformed\room-runtime-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\91127535c09fd9c7a132459495dda37f\transformed\sqlite-framework-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\91127535c09fd9c7a132459495dda37f\transformed\sqlite-framework-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.room:room-common:2.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.5.0\829a83fb92f1696a8a32f3beea884dfc87b2693\room-common-2.5.0.jar"
      resolved="androidx.room:room-common:2.5.0"/>
  <library
      name="androidx.sqlite:sqlite:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\a5e3fec7cb68cf3bd5c61b50a1136007\transformed\sqlite-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\a5e3fec7cb68cf3bd5c61b50a1136007\transformed\sqlite-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14\transforms\4af4199707c0126574c1e07760e122b9\transformed\googleid-1.1.0\jars\classes.jar"
      resolved="com.google.android.libraries.identity.googleid:googleid:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14\transforms\4af4199707c0126574c1e07760e122b9\transformed\googleid-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.26.0\c513866fd91bb46587500440a80fa943e95d12d9\error_prone_annotations-2.26.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.26.0"/>
</libraries>
