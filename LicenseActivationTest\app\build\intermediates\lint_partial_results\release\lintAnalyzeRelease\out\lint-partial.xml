<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="partial_results">
    <map id="NotificationPermission">
        <location id="class"
            file="$GRADLE_USER_HOME/caches/8.14/transforms/9b6d5929ca286945b5ef616f64e4ea48/transformed/firebase-messaging-24.0.0/jars/classes.jar"/>
        <entry
            name="className"
            string="com/google/firebase/messaging/DisplayNotification"/>
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="BackgroundService (used to send a broadcast)">
                        <location id="0"
                            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
                            line="143"
                            column="29"
                            startOffset="6223"
                            endLine="143"
                            endColumn="60"
                            endOffset="6254"/>
                        <location id="1"
                            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
                            line="149"
                            column="30"
                            startOffset="6592"
                            endLine="149"
                            endColumn="61"
                            endOffset="6623"/>
                        <location id="2"
                            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
                            line="375"
                            column="25"
                            startOffset="15760"
                            endLine="375"
                            endColumn="56"
                            endOffset="15791"/>
                        <location id="3"
                            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/DbHelper.java"
                            line="392"
                            column="25"
                            startOffset="16609"
                            endLine="392"
                            endColumn="56"
                            endOffset="16640"/>
                    </map>
                    <map id="android.intent.action.CALL (used to start an activity)">
                        <location id="0"
                            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/Dialfunction.java"
                            line="91"
                            column="33"
                            startOffset="3307"
                            endLine="91"
                            endColumn="63"
                            endOffset="3337"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.appystore.mrecharge.IncomingSms"
                    boolean="true"/>
                <entry
                    name="com.appystore.mrecharge.activity.MainActivity"
                    boolean="true"/>
                <entry
                    name="com.appystore.mrecharge.service.sever"
                    boolean="true"/>
                <entry
                    name="com.google.firebase.auth.internal.GenericIdpActivity"
                    boolean="true"/>
                <entry
                    name="com.google.firebase.auth.internal.RecaptchaActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.ic_launcher_background"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/values/ic_launcher_background.xml"
            line="3"
            column="12"
            startOffset="64"
            endLine="3"
            endColumn="41"
            endOffset="93"/>
        <location id="R.drawable.appystoremrecharge"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/appystoremrecharge.png"/>
        <location id="R.drawable.bg"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg.png"/>
        <location id="R.drawable.button_background"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="9"
            endOffset="190"/>
        <location id="R.drawable.card_background"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="9"
            endOffset="249"/>
        <location id="R.drawable.ic_launcher_background"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="170"
            endColumn="10"
            endOffset="5605"/>
        <location id="R.drawable.ic_launcher_foreground"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="50"
            endColumn="10"
            endOffset="12200"/>
        <location id="R.drawable.input_background"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/input_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="9"
            endOffset="248"/>
        <location id="R.drawable.modern_toggle_off"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_toggle_off.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="8"
            endColumn="9"
            endOffset="331"/>
        <location id="R.drawable.modern_toggle_on"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_toggle_on.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="8"
            endColumn="9"
            endOffset="331"/>
        <location id="R.drawable.modern_toggle_selector"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_toggle_selector.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="12"
            endOffset="298"/>
        <location id="R.drawable.nav_item_background"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/nav_item_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="16"
            endColumn="12"
            endOffset="510"/>
        <location id="R.layout.config"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="212"
            endColumn="16"
            endOffset="10050"/>
        <location id="R.layout.custom_dialog"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/custom_dialog.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="12"
            endColumn="18"
            endOffset="468"/>
        <location id="R.menu.bottom_navigation_menu"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="19"
            endColumn="8"
            endOffset="644"/>
        <location id="R.raw.notification"
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/raw/notification.mp3"/>
        <entry
            name="model"
            string="attr[selectableItemBackground(R)],bool[enable_system_alarm_service_default(R),enable_system_job_service_default(R),enable_system_foreground_service_default(R)],color[black(U),white(U),waiting(U),done(U),colorPrimary(U),failed(U),ic_launcher_background(D)],dimen[activity_horizontal_margin(U)],drawable[appystoremrecharge(D),bg(D),bgg(U),button_background(D),card_background(D),footer_background(U),gradient_background(U),ic_baseline_add_to_queue_24(U),ic_home_black_24dp(U),ic_launcher_background(D),ic_launcher_foreground(D),ic_settings_black_24dp(U),ic_settings_cell_black_24dp(U),ic_stat_name(U),input_background(D),modern_button_background(U),modern_edit_text_background(U),modern_status_background(U),modern_toggle_off(D),modern_toggle_on(D),modern_toggle_selector(D),nav_item_background(D),rightorder(U),rounded_corner(U),rounded_cornerss(U),save(U),select(U),toggle_off(U),toggle_on(U),toggle_selector(U)],id[activity_main(D),license_activation(U),btnSpeakContainer(U),home(U),bank(U),sms(U),mon(U),imageView(D),myid(U),licensekey(U),dpin(U),server_type(U),olmd(U),newm(U),sav(U),simaname(U),simbname(U),simsms(D),simbc(D),sms_service(U),auto(U),bettery(U),next(U),gpt(D),gp(D),blt(D),robi(D),sima_rocketj(D),at(D),sima_rocketm(D),bl(D),ttt(D),tt(D),save(D),dialog_button(D),bks(U),bkash(U),rks(U),rocket(U),ngs(U),nogad(U),time(U),trys(U),blb(U),dc(U),savedata(U),itemLayout(D),status(U),tvPrice(U),main(D),licenseKeyField(U),pinField(U),activateButton(U),statusText(D),domainLoginSection(D),domainInfoText(D),domainLoginButton(D),expirationSection(D),expirationText(D),countdownText(D),expirationProgressBar(D),recycleView(U),upay(U),bill(U),inter(U),forward(U)],integer[google_play_services_version(R)],layout[activity_main(U),license_activation(U),config(D),custom_dialog(D),forward(U),item_todo(U),listmain(U),settingsd(U)],menu[bottom_navigation_menu(D),menu_main(U)],mipmap[ic_launcher(U)],raw[notification(D)],string[app_name(U),accessibility_service_description(U)],style[Theme_AppyStoreMRecharge(U),Base_Theme_AppyStoreMRecharge(U),Theme_Material3_DayNight_NoActionBar(R)],xml[data_extraction_rules(U),backup_rules(U),network_security_config(U),ussd_service(U)];20^1f^1e,29^28^27,6d^e^6e^0^2e^14^2f^2d^18^30^17^13^77^24^23^29,6e^12^1c^1b^1d^11,6f^e^22^14^26^18^17^b^23^25,71^e^4^22^14^5^18^26^17^25^23^55,74^e^22^14^26^18^17^b^77^23^5^29,75^14^18^17^13,7b^7c,7c^7d,81^7a;;;"/>
    </map>

</incidents>
