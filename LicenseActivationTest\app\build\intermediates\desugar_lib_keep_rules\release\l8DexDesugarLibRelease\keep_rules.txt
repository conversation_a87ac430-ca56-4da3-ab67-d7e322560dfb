-keep class j$.time.Duration {
  public int compareTo(j$.time.Duration);
  public int getNano();
  public long getSeconds();
  public static j$.time.Duration ofMillis(long);
  public static j$.time.Duration ofSeconds(long, long);
  public long toMillis();
  j$.time.Duration ZERO;
}
-keep class j$.time.Instant {
  public j$.time.OffsetDateTime atOffset(j$.time.ZoneOffset);
  public long getEpochSecond();
  public int getNano();
  public static j$.time.Instant ofEpochMilli(long);
  public static j$.time.Instant ofEpochSecond(long, long);
  public long toEpochMilli();
}
-keep class j$.time.LocalDateTime {
  public java.lang.String format(j$.time.format.DateTimeFormatter);
}
-keep class j$.time.OffsetDateTime {
  public j$.time.LocalDateTime toLocalDateTime();
}
-keep class j$.time.TimeConversions {
  public static java.time.Duration convert(j$.time.Duration);
  public static j$.time.Duration convert(java.time.Duration);
}
-keep class j$.time.ZoneOffset {
  j$.time.ZoneOffset UTC;
}
-keep class j$.time.format.DateTimeFormatter {
  j$.time.format.DateTimeFormatter ISO_LOCAL_DATE;
}
-keep enum j$.time.temporal.ChronoUnit {
  public j$.time.Duration getDuration();
  j$.time.temporal.ChronoUnit MILLIS;
}
-keep class j$.util.DateRetargetClass {
  public static j$.time.Instant toInstant(java.util.Date);
}
-keep class j$.util.DesugarTimeZone {
  public static java.util.TimeZone getTimeZone(java.lang.String);
}
