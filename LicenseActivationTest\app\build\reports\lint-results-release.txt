C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:215: Error: Class referenced in the manifest, com.google.firebase.auth.api.fallback.service.FirebaseAuthFallbackService, was not found in the project or the libraries [MissingClass]
            android:name="com.google.firebase.auth.api.fallback.service.FirebaseAuthFallbackService"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:280: Error: Class referenced in the manifest, com.google.android.gms.measurement.AppMeasurementReceiver, was not found in the project or the libraries [MissingClass]
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:284: Error: Class referenced in the manifest, com.google.android.gms.measurement.AppMeasurementService, was not found in the project or the libraries [MissingClass]
            android:name="com.google.android.gms.measurement.AppMeasurementService"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:288: Error: Class referenced in the manifest, com.google.android.gms.measurement.AppMeasurementJobService, was not found in the project or the libraries [MissingClass]
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:296: Error: Class referenced in the manifest, androidx.room.MultiInstanceInvalidationService, was not found in the project or the libraries [MissingClass]
            android:name="androidx.room.MultiInstanceInvalidationService"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingClass":
   If a class is referenced in the manifest or in a layout file, it must also
   exist in the project (or in one of the libraries included by the project.
   This check helps uncover typos in registration names, or attempts to rename
   or move classes without updating the XML references properly.

   https://developer.android.com/guide/topics/manifest/manifest-intro.html

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:88: Warning: This LinearLayout should use android:layout_height="wrap_content" [ScrollViewSize]
            android:layout_height="match_parent">
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:118: Warning: This LinearLayout should use android:layout_height="wrap_content" [ScrollViewSize]
            android:layout_height="match_parent"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:92: Warning: This LinearLayout should use android:layout_height="wrap_content" [ScrollViewSize]
            android:layout_height="match_parent">
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScrollViewSize":
   ScrollView children must set their layout_width or layout_height attributes
   to wrap_content rather than fill_parent or match_parent in the scrolling
   dimension.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:497: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
        edit.commit();
             ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java:61: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                edit.commit();
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java:156: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
        edit.commit();
             ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java:179: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                        edit.commit();
                             ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:341: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                edit.commit();
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:374: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                edit.commit();
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:412: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                edit.commit();
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:422: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                edit.commit();
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:475: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                edit.commit();
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:608: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
        edit.commit();
             ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Monitoring.java:97: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
        edit.commit();
             ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java:303: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
        edit.commit();
             ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:532: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
        edit.commit();
             ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:536: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
        PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit().remove(str).commit();
                                                                                                  ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\intsetting.java:83: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                edit.commit();
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\intsetting.java:101: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                edit.commit();
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\intsetting.java:119: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                edit.commit();
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\intsetting.java:137: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                edit.commit();
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\intsetting.java:155: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
                edit.commit();
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\intsetting.java:181: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
        edit.commit();
             ~~~~~~~~

   Explanation for issues of type "ApplySharedPref":
   Consider using apply() instead of commit on shared preferences. Whereas
   commit blocks and writes its data to persistent storage immediately, apply
   will handle it in the background.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:265: Warning: The id R.id.licensekey has already been looked up in this method; possible cut & paste error? [CutPasteId]
        this.url = (EditText) findViewById(R.id.licensekey);
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:158: First usage here
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:266: Warning: The id R.id.dpin has already been looked up in this method; possible cut & paste error? [CutPasteId]
        this.mpin = (EditText) findViewById(R.id.dpin);
                               ~~~~~~~~~~~~~~~~~~~~~~~
    C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:159: First usage here

   Explanation for issues of type "CutPasteId":
   This lint check looks for cases where you have cut & pasted calls to
   findViewById but have forgotten to update the R.id field. It's possible
   that your code is simply (redundantly) looking up the field repeatedly, but
   lint cannot distinguish that from a case where you for example want to
   initialize fields prev and next and you cut & pasted
   findViewById(R.id.prev) and forgot to update the second initialization to
   R.id.next.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:595: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String code = String.format("%010d", combined);
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java:44: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            } else if ("30D @598Tk (C: 90)\n4. Free 4G SIM at 63Tk Recharge \n5. Flash Drive 510mins, 30D@TK307 (C:45)\n p. Prev \n#. Next".toLowerCase().indexOf("next") >= 0) {
                                                                                                                                            ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java:77: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        return (str == null || str.length() == 0) ? "" : str.toLowerCase();
                                                             ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java:95: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        String tkfindfinall = tkfindfinall(readLine.toLowerCase());
                                                                    ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java:98: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                            System.out.println("goo " + readLine.toLowerCase().indexOf("main"));
                                                                 ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java:218: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        if (readLine.toLowerCase().indexOf("tk " + intValue) >= 0) {
                                     ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java:242: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        if (readLine.toLowerCase().indexOf(intValue + "tk") >= 0) {
                                     ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java:275: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        return !TextUtils.isEmpty(str2) ? str2.toLowerCase() : "osman";
                                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:156: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("ussd") ||
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:157: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("balance") ||
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:158: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("recharge") ||
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:159: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("tk") ||
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:160: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("taka") ||
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:161: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("successful") ||
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:162: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("failed"))) {
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:246: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerResponse = response.toLowerCase();
                                        ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:314: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                if (text != null && text.toString().toLowerCase().contains(buttonText.toLowerCase())) {
                                                    ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:314: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                if (text != null && text.toString().toLowerCase().contains(buttonText.toLowerCase())) {
                                                                                      ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:358: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        String tkfindfinall = tkfindfinall(readLine.toLowerCase());
                                                                    ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:389: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    if (text.toString().toLowerCase().equals("ok") || text.toString().toLowerCase().equals("cancel")) {
                                        ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:389: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    if (text.toString().toLowerCase().equals("ok") || text.toString().toLowerCase().equals("cancel")) {
                                                                                      ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:405: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        } else if (text2 != null && child2.getClassName().equals(Button.class.getName()) && (text2.toString().toLowerCase().equals("ok") || text2.toString().toLowerCase().equals("cancel"))) {
                                                                                                                              ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:405: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        } else if (text2 != null && child2.getClassName().equals(Button.class.getName()) && (text2.toString().toLowerCase().equals("ok") || text2.toString().toLowerCase().equals("cancel"))) {
                                                                                                                                                                             ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:481: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        if (readLine.toLowerCase().indexOf(intValue + "tk") >= 0) {
                                     ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:504: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        if (readLine.toLowerCase().indexOf("tk " + intValue) >= 0) {
                                     ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:514: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        return (str == null || str.length() == 0) ? "" : str.toLowerCase();
                                                             ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:562: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        return !TextUtils.isEmpty(tkfind2) ? tkfind2.toLowerCase() : "osman";
                                                     ~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:210: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                i = cursor.getInt(cursor.getColumnIndex("resend"));
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:229: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                str5 = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_NUMBER));
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:230: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                str3 = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_AMOUNT));
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:231: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                str4 = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_TIME));
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:319: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                this.number = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_NUMBER));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:320: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                this.operator = cursor.getString(cursor.getColumnIndex("operator"));
                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:321: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                this.amount = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_AMOUNT));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:322: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                this.extra = cursor.getString(cursor.getColumnIndex("extra"));
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:323: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                this.id = cursor.getInt(cursor.getColumnIndex(CONTACTS_COLUMN_ID));
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:436: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                i = cursor.getInt(cursor.getColumnIndex("pos"));
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:453: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                i = cursor.getInt(cursor.getColumnIndex("sim"));
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java:49: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                i2 = Integer.parseInt(data.getString(data.getColumnIndex(DbHelper.CONTACTS_COLUMN_ID)));
                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java:55: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
            int i4 = data.getInt(data.getColumnIndex("status"));
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Recharge.java:83: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String orderId = cursor.getString(cursor.getColumnIndex("orderid"));
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Recharge.java:84: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String ussdCode = cursor.getString(cursor.getColumnIndex("ussd"));
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Recharge.java:85: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String slotStr = cursor.getString(cursor.getColumnIndex("slot"));
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Recharge.java:86: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String id = cursor.getString(cursor.getColumnIndex(DbHelper.CONTACTS_COLUMN_ID));
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Range":
   Some parameters are required to be in a particular numerical range; this
   check makes sure that arguments passed fall within the range. For arrays,
   Strings and collections this refers to the size or length.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java:117: Warning: Expected duration Toast.LENGTH_SHORT or Toast.LENGTH_LONG, a custom duration value is not supported [ShowToast]
            Toast.makeText(this.appContext, "Error on function 1", 0).show();
                                                                   ~

   Explanation for issues of type "ShowToast":
   Toast.makeText() creates a Toast but does not show it. You must call show()
   on the resulting object to actually make the Toast appear.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\util\NotificationUtils.java:172: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SimpleDateFormat":
   Almost all callers should use getDateInstance(), getDateTimeInstance(), or
   getTimeInstance() to get a ready-made instance of SimpleDateFormat suitable
   for the user's locale. The main reason you'd create an instance this class
   directly is because you need to format/parse a specific machine-readable
   format, in which case you almost certainly want to explicitly ask for US to
   ensure that you get ASCII digits (rather than, say, Arabic digits).

   Therefore, you should either use the form of the SimpleDateFormat
   constructor where you pass in an explicit locale, such as Locale.US, or use
   one of the get instance methods, or suppress this error if really know what
   you are doing.

   https://developer.android.com/reference/java/text/SimpleDateFormat.html

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:470: Error: Must be one of: Context.POWER_SERVICE, Context.WINDOW_SERVICE, Context.LAYOUT_INFLATER_SERVICE, Context.ACCOUNT_SERVICE, Context.ACTIVITY_SERVICE, Context.ALARM_SERVICE, Context.NOTIFICATION_SERVICE, Context.ACCESSIBILITY_SERVICE, Context.CAPTIONING_SERVICE, Context.KEYGUARD_SERVICE, Context.LOCATION_SERVICE, Context.HEALTHCONNECT_SERVICE, Context.SEARCH_SERVICE, Context.SENSOR_SERVICE, Context.STORAGE_SERVICE, Context.STORAGE_STATS_SERVICE, Context.WALLPAPER_SERVICE, Context.VIBRATOR_MANAGER_SERVICE, Context.VIBRATOR_SERVICE, Context.CONNECTIVITY_SERVICE, Context.IPSEC_SERVICE, Context.VPN_MANAGEMENT_SERVICE, Context.NETWORK_STATS_SERVICE, Context.WIFI_SERVICE, Context.WIFI_AWARE_SERVICE, Context.WIFI_P2P_SERVICE, Context.WIFI_RTT_RANGING_SERVICE, Context.NSD_SERVICE, Context.AUDIO_SERVICE, Context.FINGERPRINT_SERVICE, Context.BIOMETRIC_SERVICE, Context.MEDIA_ROUTER_SERVICE, Context.TELEPHONY_SERVICE, Context.TELEPHONY_SUBSCRIPTION_SERVICE, Context.CARRIER_CONFIG_SERVICE, Context.EUICC_SERVICE, Context.TELECOM_SERVICE, Context.CLIPBOARD_SERVICE, Context.INPUT_METHOD_SERVICE, Context.TEXT_SERVICES_MANAGER_SERVICE, Context.TEXT_CLASSIFICATION_SERVICE, Context.APPWIDGET_SERVICE, Context.DROPBOX_SERVICE, Context.DEVICE_POLICY_SERVICE, Context.UI_MODE_SERVICE, Context.DOWNLOAD_SERVICE, Context.NFC_SERVICE, Context.BLUETOOTH_SERVICE, Context.USB_SERVICE, Context.LAUNCHER_APPS_SERVICE, Context.INPUT_SERVICE, Context.DISPLAY_SERVICE, Context.USER_SERVICE, Context.RESTRICTIONS_SERVICE, Context.APP_OPS_SERVICE, Context.ROLE_SERVICE, Context.CAMERA_SERVICE, Context.PRINT_SERVICE, Context.CONSUMER_IR_SERVICE, Context.TV_INTERACTIVE_APP_SERVICE, Context.TV_INPUT_SERVICE, Context.USAGE_STATS_SERVICE, Context.MEDIA_SESSION_SERVICE, Context.MEDIA_COMMUNICATION_SERVICE, Context.BATTERY_SERVICE, Context.JOB_SCHEDULER_SERVICE, Context.PERSISTENT_DATA_BLOCK_SERVICE, Context.MEDIA_PROJECTION_SERVICE, Context.MIDI_SERVICE, Context.HARDWARE_PROPERTIES_SERVICE, Context.SHORTCUT_SERVICE, Context.SYSTEM_HEALTH_SERVICE, Context.COMPANION_DEVICE_SERVICE, Context.VIRTUAL_DEVICE_SERVICE, Context.CROSS_PROFILE_APPS_SERVICE, Context.LOCALE_SERVICE, Context.MEDIA_METRICS_SERVICE, Context.DISPLAY_HASH_SERVICE, Context.CREDENTIAL_SERVICE, Context.DEVICE_LOCK_SERVICE, Context.GRAMMATICAL_INFLECTION_SERVICE, Context.SECURITY_STATE_SERVICE, Context.CONTACT_KEYS_SERVICE [WrongConstant]
        NetworkInfo activeNetworkInfo = ((ConnectivityManager) context.getSystemService("connectivity")).getActiveNetworkInfo();
                                                                                        ~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java:117: Error: Must be one of: Toast.LENGTH_SHORT, Toast.LENGTH_LONG [WrongConstant]
            Toast.makeText(this.appContext, "Error on function 1", 0).show();
                                                                   ~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:612: Error: Must be one of: Context.POWER_SERVICE, Context.WINDOW_SERVICE, Context.LAYOUT_INFLATER_SERVICE, Context.ACCOUNT_SERVICE, Context.ACTIVITY_SERVICE, Context.ALARM_SERVICE, Context.NOTIFICATION_SERVICE, Context.ACCESSIBILITY_SERVICE, Context.CAPTIONING_SERVICE, Context.KEYGUARD_SERVICE, Context.LOCATION_SERVICE, Context.HEALTHCONNECT_SERVICE, Context.SEARCH_SERVICE, Context.SENSOR_SERVICE, Context.STORAGE_SERVICE, Context.STORAGE_STATS_SERVICE, Context.WALLPAPER_SERVICE, Context.VIBRATOR_MANAGER_SERVICE, Context.VIBRATOR_SERVICE, Context.CONNECTIVITY_SERVICE, Context.IPSEC_SERVICE, Context.VPN_MANAGEMENT_SERVICE, Context.NETWORK_STATS_SERVICE, Context.WIFI_SERVICE, Context.WIFI_AWARE_SERVICE, Context.WIFI_P2P_SERVICE, Context.WIFI_RTT_RANGING_SERVICE, Context.NSD_SERVICE, Context.AUDIO_SERVICE, Context.FINGERPRINT_SERVICE, Context.BIOMETRIC_SERVICE, Context.MEDIA_ROUTER_SERVICE, Context.TELEPHONY_SERVICE, Context.TELEPHONY_SUBSCRIPTION_SERVICE, Context.CARRIER_CONFIG_SERVICE, Context.EUICC_SERVICE, Context.TELECOM_SERVICE, Context.CLIPBOARD_SERVICE, Context.INPUT_METHOD_SERVICE, Context.TEXT_SERVICES_MANAGER_SERVICE, Context.TEXT_CLASSIFICATION_SERVICE, Context.APPWIDGET_SERVICE, Context.DROPBOX_SERVICE, Context.DEVICE_POLICY_SERVICE, Context.UI_MODE_SERVICE, Context.DOWNLOAD_SERVICE, Context.NFC_SERVICE, Context.BLUETOOTH_SERVICE, Context.USB_SERVICE, Context.LAUNCHER_APPS_SERVICE, Context.INPUT_SERVICE, Context.DISPLAY_SERVICE, Context.USER_SERVICE, Context.RESTRICTIONS_SERVICE, Context.APP_OPS_SERVICE, Context.ROLE_SERVICE, Context.CAMERA_SERVICE, Context.PRINT_SERVICE, Context.CONSUMER_IR_SERVICE, Context.TV_INTERACTIVE_APP_SERVICE, Context.TV_INPUT_SERVICE, Context.USAGE_STATS_SERVICE, Context.MEDIA_SESSION_SERVICE, Context.MEDIA_COMMUNICATION_SERVICE, Context.BATTERY_SERVICE, Context.JOB_SCHEDULER_SERVICE, Context.PERSISTENT_DATA_BLOCK_SERVICE, Context.MEDIA_PROJECTION_SERVICE, Context.MIDI_SERVICE, Context.HARDWARE_PROPERTIES_SERVICE, Context.SHORTCUT_SERVICE, Context.SYSTEM_HEALTH_SERVICE, Context.COMPANION_DEVICE_SERVICE, Context.VIRTUAL_DEVICE_SERVICE, Context.CROSS_PROFILE_APPS_SERVICE, Context.LOCALE_SERVICE, Context.MEDIA_METRICS_SERVICE, Context.DISPLAY_HASH_SERVICE, Context.CREDENTIAL_SERVICE, Context.DEVICE_LOCK_SERVICE, Context.GRAMMATICAL_INFLECTION_SERVICE, Context.SECURITY_STATE_SERVICE, Context.CONTACT_KEYS_SERVICE [WrongConstant]
        Iterator<ActivityManager.RunningServiceInfo> it = ((ActivityManager) getSystemService("activity")).getRunningServices(Integer.MAX_VALUE).iterator();
                                                                                              ~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:650: Error: Must be one of: Context.POWER_SERVICE, Context.WINDOW_SERVICE, Context.LAYOUT_INFLATER_SERVICE, Context.ACCOUNT_SERVICE, Context.ACTIVITY_SERVICE, Context.ALARM_SERVICE, Context.NOTIFICATION_SERVICE, Context.ACCESSIBILITY_SERVICE, Context.CAPTIONING_SERVICE, Context.KEYGUARD_SERVICE, Context.LOCATION_SERVICE, Context.HEALTHCONNECT_SERVICE, Context.SEARCH_SERVICE, Context.SENSOR_SERVICE, Context.STORAGE_SERVICE, Context.STORAGE_STATS_SERVICE, Context.WALLPAPER_SERVICE, Context.VIBRATOR_MANAGER_SERVICE, Context.VIBRATOR_SERVICE, Context.CONNECTIVITY_SERVICE, Context.IPSEC_SERVICE, Context.VPN_MANAGEMENT_SERVICE, Context.NETWORK_STATS_SERVICE, Context.WIFI_SERVICE, Context.WIFI_AWARE_SERVICE, Context.WIFI_P2P_SERVICE, Context.WIFI_RTT_RANGING_SERVICE, Context.NSD_SERVICE, Context.AUDIO_SERVICE, Context.FINGERPRINT_SERVICE, Context.BIOMETRIC_SERVICE, Context.MEDIA_ROUTER_SERVICE, Context.TELEPHONY_SERVICE, Context.TELEPHONY_SUBSCRIPTION_SERVICE, Context.CARRIER_CONFIG_SERVICE, Context.EUICC_SERVICE, Context.TELECOM_SERVICE, Context.CLIPBOARD_SERVICE, Context.INPUT_METHOD_SERVICE, Context.TEXT_SERVICES_MANAGER_SERVICE, Context.TEXT_CLASSIFICATION_SERVICE, Context.APPWIDGET_SERVICE, Context.DROPBOX_SERVICE, Context.DEVICE_POLICY_SERVICE, Context.UI_MODE_SERVICE, Context.DOWNLOAD_SERVICE, Context.NFC_SERVICE, Context.BLUETOOTH_SERVICE, Context.USB_SERVICE, Context.LAUNCHER_APPS_SERVICE, Context.INPUT_SERVICE, Context.DISPLAY_SERVICE, Context.USER_SERVICE, Context.RESTRICTIONS_SERVICE, Context.APP_OPS_SERVICE, Context.ROLE_SERVICE, Context.CAMERA_SERVICE, Context.PRINT_SERVICE, Context.CONSUMER_IR_SERVICE, Context.TV_INTERACTIVE_APP_SERVICE, Context.TV_INPUT_SERVICE, Context.USAGE_STATS_SERVICE, Context.MEDIA_SESSION_SERVICE, Context.MEDIA_COMMUNICATION_SERVICE, Context.BATTERY_SERVICE, Context.JOB_SCHEDULER_SERVICE, Context.PERSISTENT_DATA_BLOCK_SERVICE, Context.MEDIA_PROJECTION_SERVICE, Context.MIDI_SERVICE, Context.HARDWARE_PROPERTIES_SERVICE, Context.SHORTCUT_SERVICE, Context.SYSTEM_HEALTH_SERVICE, Context.COMPANION_DEVICE_SERVICE, Context.VIRTUAL_DEVICE_SERVICE, Context.CROSS_PROFILE_APPS_SERVICE, Context.LOCALE_SERVICE, Context.MEDIA_METRICS_SERVICE, Context.DISPLAY_HASH_SERVICE, Context.CREDENTIAL_SERVICE, Context.DEVICE_LOCK_SERVICE, Context.GRAMMATICAL_INFLECTION_SERVICE, Context.SECURITY_STATE_SERVICE, Context.CONTACT_KEYS_SERVICE [WrongConstant]
        Iterator<AccessibilityServiceInfo> it = ((AccessibilityManager) context.getSystemService("accessibility")).getEnabledAccessibilityServiceList(-1).iterator();
                                                                                                 ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:445: Error: Must be one or more of: AccessibilityServiceInfo.FEEDBACK_AUDIBLE, AccessibilityServiceInfo.FEEDBACK_GENERIC, AccessibilityServiceInfo.FEEDBACK_HAPTIC, AccessibilityServiceInfo.FEEDBACK_SPOKEN, AccessibilityServiceInfo.FEEDBACK_VISUAL, AccessibilityServiceInfo.FEEDBACK_BRAILLE [WrongConstant]
        accessibilityServiceInfo.feedbackType = 16;
                                                ~~

   Explanation for issues of type "WrongConstant":
   Ensures that when parameter in a method only allows a specific set of
   constants, calls obey those rules.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:149: Warning: Declaring a broadcastreceiver for android.net.conn.CONNECTIVITY_CHANGE is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use WorkManager. [BatteryLife]
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "BatteryLife":
   This issue flags code that either
   * negatively affects battery life, or
   * uses APIs that have recently changed behavior to prevent background tasks
   from consuming memory and battery excessively.

   Generally, you should be using WorkManager instead.

   For more details on how to update your code, please see
   https://developer.android.com/topic/performance/background-optimization


C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\International_service.java:127: Error: To call Service.startForeground(), the <service> element of manifest file must have the foregroundServiceType attribute specified [ForegroundServiceType]
        startForeground(NOTIFICATION_ID, builder.build());
        ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Recharge.java:164: Error: To call Service.startForeground(), the <service> element of manifest file must have the foregroundServiceType attribute specified [ForegroundServiceType]
        startForeground(NOTIFICATION_ID, builder.build());
        ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Smsend.java:161: Error: To call Service.startForeground(), the <service> element of manifest file must have the foregroundServiceType attribute specified [ForegroundServiceType]
        startForeground(NOTIFICATION_ID, builder.build());
        ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\sever.java:199: Error: To call Service.startForeground(), the <service> element of manifest file must have the foregroundServiceType attribute specified [ForegroundServiceType]
        startForeground(1, notificationBuilder.build());
        ~~~~~~~~~~~~~~~

   Explanation for issues of type "ForegroundServiceType":
   For targetSdkVersion >= 34, to call Service.startForeground(), the
   <service> element in the manifest file must have the foregroundServiceType
   attribute specified.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:43: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Smsend.java:204: Error: smsSentReceiver is missing RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED flag for unprotected broadcasts registered for SMS_SENT [UnspecifiedRegisterReceiverFlag]
            registerReceiver(smsSentReceiver, new IntentFilter("SMS_SENT"));
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Smsend.java:207: Error: smsDeliveredReceiver is missing RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED flag for unprotected broadcasts registered for SMS_DELIVERED [UnspecifiedRegisterReceiverFlag]
            registerReceiver(smsDeliveredReceiver, new IntentFilter("SMS_DELIVERED"));
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\sever.java:728: Error: smsSentReceiver is missing RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED flag for unprotected broadcasts registered for SMS_SENT [UnspecifiedRegisterReceiverFlag]
            registerReceiver(smsSentReceiver, new IntentFilter("SMS_SENT"));
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\sever.java:729: Error: smsDeliveredReceiver is missing RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED flag for unprotected broadcasts registered for SMS_DELIVERED [UnspecifiedRegisterReceiverFlag]
            registerReceiver(smsDeliveredReceiver, new IntentFilter("SMS_DELIVERED"));
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnspecifiedRegisterReceiverFlag":
   In Android U, all receivers registering for non-system broadcasts are
   required to include a flag indicating the receiver's exported state. Apps
   registering for non-system broadcasts should use the
   ContextCompat#registerReceiver APIs with flags set to either
   RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED.

   If you are not expecting broadcasts from other apps on the device, register
   your receiver with RECEIVER_NOT_EXPORTED to protect your receiver on all
   platform releases.

   https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.10.1 is available: 8.11.0 [AndroidGradlePluginVersion]
agp = "8.10.1"
      ~~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle:34: Warning: A newer version of com.android.tools:desugar_jdk_libs than 2.1.3 is available: 2.1.5 [GradleDependency]
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.3'
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle:48: Warning: A newer version of com.squareup.okhttp3:okhttp than 4.11.0 is available: 4.12.0 [GradleDependency]
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle:49: Warning: A newer version of com.squareup.okhttp3:logging-interceptor than 4.11.0 is available: 4.12.0 [GradleDependency]
    implementation 'com.squareup.okhttp3:logging-interceptor:4.11.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle:52: Warning: A newer version of androidx.annotation:annotation than 1.7.1 is available: 1.9.1 [GradleDependency]
    implementation 'androidx.annotation:annotation:1.7.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml:6: Warning: A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1 [GradleDependency]
appcompat = "1.7.0"
            ~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml:13: Warning: A newer version of com.google.firebase:firebase-messaging than 24.0.0 is available: 24.1.2 [GradleDependency]
firebase-messaging = "24.0.0"
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml:14: Warning: A newer version of com.google.firebase:firebase-bom than 32.8.0 is available: 33.16.0 [GradleDependency]
firebase-bom = "32.8.0"
               ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml:15: Warning: A newer version of com.google.android.gms:play-services-base than 18.3.0 is available: 18.7.0 [GradleDependency]
play-services-base = "18.3.0"
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml:16: Warning: A newer version of androidx.work:work-runtime than 2.9.0 is available: 2.10.2 [GradleDependency]
work-runtime = "2.9.0"
               ~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml:19: Warning: A newer version of com.google.firebase:firebase-auth than 23.2.0 is available: 23.2.1 [GradleDependency]
firebaseAuth = "23.2.0"
               ~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\util\Devicer.java:15: Warning: Method Devicer looks like a constructor but is a normal method [NotConstructor]
    @FormUrlEncoded
    ^

   Explanation for issues of type "NotConstructor":
   This check catches methods that look like they were intended to be
   constructors, but aren't.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:109: Warning: Use '$' instead of '.' for inner classes; replace "androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver" with "androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver" [InnerclassSeparator]
            android:name="androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:114: Warning: Use '$' instead of '.' for inner classes; replace "androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy" with "androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy" [InnerclassSeparator]
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:124: Warning: Use '$' instead of '.' for inner classes; replace "androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy" with "androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy" [InnerclassSeparator]
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:134: Warning: Use '$' instead of '.' for inner classes; replace "androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy" with "androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy" [InnerclassSeparator]
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:144: Warning: Use '$' instead of '.' for inner classes; replace "androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy" with "androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy" [InnerclassSeparator]
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "InnerclassSeparator":
   When you reference an inner class in a manifest file, you must use '$'
   instead of '.' as the separator character, i.e. Outer$Inner instead of
   Outer.Inner.

   (If you get this warning for a class which is not actually an inner class,
   it's because you are using uppercase characters in your package name, which
   is not conventional.)

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\custom_dialog.xml:7: Warning: Avoid using "px" as units; use "dp" instead [PxUsage]
        android:layout_width="100px"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "PxUsage":
   For performance reasons and to keep the code simpler, the Android system
   uses pixels as the standard unit for expressing dimension or coordinate
   values. That means that the dimensions of a view are always expressed in
   the code using pixels, but always based on the current screen density. For
   instance, if myView.getWidth() returns 10, the view is 10 pixels wide on
   the current screen, but on a device with a higher density screen, the value
   returned might be 15. If you use pixel values in your application code to
   work with bitmaps that are not pre-scaled for the current screen density,
   you might need to scale the pixel values that you use in your code to match
   the un-scaled bitmap source.

   https://developer.android.com/guide/practices/screens_support.html#screen-independence

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:1371: Warning: Using getString to get device identifiers is not recommended [HardwareIds]
                            "&device_id=" + Settings.Secure.getString(MainActivity.this.getContentResolver(), "android_id");
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardwareIds":
   Using these device identifiers is not recommended other than for high value
   fraud prevention and advanced telephony use-cases. For advertising
   use-cases, use AdvertisingIdClient$Info#getId and for analytics, use
   InstanceId#getId.

   https://developer.android.com/training/articles/user-data-ids.html

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:50: Warning: BroadcastReceivers that declare an intent-filter for SMS_DELIVER or SMS_RECEIVED must ensure that the caller has the BROADCAST_SMS permission, otherwise it is possible for malicious actors to spoof intents [UnprotectedSMSBroadcastReceiver]
        <receiver android:name="com.appystore.mrecharge.IncomingSms"
         ~~~~~~~~

   Explanation for issues of type "UnprotectedSMSBroadcastReceiver":
   BroadcastReceivers that declare an intent-filter for SMS_DELIVER or
   SMS_RECEIVED must ensure that the caller has the BROADCAST_SMS permission,
   otherwise it is possible for malicious actors to spoof intents.

   https://goo.gle/UnprotectedSMSBroadcastReceiver

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:63: Warning: Exported service does not require permission [ExportedService]
        <service
         ~~~~~~~

   Explanation for issues of type "ExportedService":
   Exported services (services which either set exported=true or contain an
   intent-filter and do not specify exported=false) should define a permission
   that an entity must have in order to launch the service or bind to it.
   Without this, any application can use this service.

   https://goo.gle/ExportedService

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\xml\network_security_config.xml:3: Warning: Insecure Base Configuration [InsecureBaseConfiguration]
    <base-config cleartextTrafficPermitted="true">
                                            ~~~~

   Explanation for issues of type "InsecureBaseConfiguration":
   Permitting cleartext traffic could allow eavesdroppers to intercept data
   sent by your app, which impacts the privacy of your users. Consider only
   allowing encrypted traffic by setting the cleartextTrafficPermitted tag to
   false.

   https://goo.gle/InsecureBaseConfiguration
   https://developer.android.com/preview/features/security-config.html

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Monitoring.java:137: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                    this.recyclerviewItemAdapter.notifyDataSetChanged();
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Monitoring.java:188: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                    this.recyclerviewItemAdapter.notifyDataSetChanged();
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Monitoring.java:199: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                this.recyclerviewItemAdapter.notifyDataSetChanged();
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotifyDataSetChanged":
   The RecyclerView adapter's onNotifyDataSetChanged method does not specify
   what about the data set has changed, forcing any observers to assume that
   all existing items and structure may no longer be valid. `LayoutManager`s
   will be forced to fully rebind and relayout all visible views.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:71: Warning: This Cursor should be freed up after use with #close() [Recycle]
        return readableDatabase.rawQuery(sb.toString(), null).getCount() == 0;
                                ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:189: Warning: This Cursor should be freed up after use with #close() [Recycle]
        return readableDatabase.rawQuery(sb.toString(), null).getCount() == 0;
                                ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:200: Warning: This Cursor should be freed up after use with #close() [Recycle]
        return readableDatabase.rawQuery(sb.toString(), null).getCount() == 0;
                                ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java:285: Warning: This Cursor should be freed up after use with #close() [Recycle]
            if (writableDatabase.rawQuery("select * from income_log where status!=0", null).getCount() > 40) {
                                 ~~~~~~~~

   Explanation for issues of type "Recycle":
   Many resources, such as TypedArrays, VelocityTrackers, etc., should be
   recycled (with a recycle() call) after use. This lint check looks for
   missing recycle() calls.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:97: Warning: Invalid layout param in a LinearLayout: layout_alignParentLeft [ObsoleteLayoutParam]
                android:layout_alignParentLeft="true">
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:208: Warning: Invalid layout param in a LinearLayout: layout_alignParentRight [ObsoleteLayoutParam]
                    android:layout_alignParentRight="true"/>
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:287: Warning: Invalid layout param in a LinearLayout: layout_below [ObsoleteLayoutParam]
                android:layout_below="@+id/blb"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteLayoutParam":
   The given layout_param is not defined for the given layout, meaning it has
   no effect. This usually happens when you change the parent layout or move
   view code around without updating the layout params. This will cause
   useless attribute processing at runtime, and is misleading for others
   reading the layout so the parameter should be removed.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java:140: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (telecomManager != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:305: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= 23) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:388: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                if (Build.VERSION.SDK_INT >= 23) {
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:561: Warning: Unnecessary; SDK_INT is never < 24 [ObsoleteSdkInt]
        if (i != ACTION_MANAGE_OVERLAY_PERMISSION_REQUEST_CODE || Build.VERSION.SDK_INT < 23 || android.provider.Settings.canDrawOverlays(getApplicationContext())) {
                                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:600: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= 23) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:1297: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= 23) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\util\NotificationUtils.java:146: Warning: Unnecessary; SDK_INT is never < 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.KITKAT_WATCH) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\PushService.java:142: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Smsend.java:194: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable-v24: Warning: This folder configuration (v24) is unnecessary; minSdkVersion is 24. Merge all the resources in this folder into drawable. [ObsoleteSdkInt]

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:833: Warning: This AsyncTask class should be static or leaks might occur (com.appystore.mrecharge.activity.MainActivity.Checkstatus) [StaticFieldLeak]
    private class Checkstatus extends AsyncTask<Void, Void, Boolean> {
                  ~~~~~~~~~~~

   Explanation for issues of type "StaticFieldLeak":
   A static field will leak contexts.

   Non-static inner classes have an implicit reference to their outer class.
   If that outer class is for example a Fragment or Activity, then this
   reference means that the long-running handler/loader/task will hold a
   reference to the activity which prevents it from getting garbage
   collected.

   Similarly, direct field references to activities and fragments from these
   longer running instances can cause leaks.

   ViewModel classes should never point to Views or non-application Contexts.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_launcher_foreground.xml:20: Warning: Very long vector path (1089 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      <path android:pathData="M395.54688,-27.21875Q395.54688,-32.625,391.70312,-35.640625Q387.875,-38.671875,378.23438,-41.90625Q368.60938,-45.140625,362.5,-48.171875Q342.59375,-57.9375,342.59375,-75.03125Q342.59375,-83.53125,347.54688,-90.03125Q352.51562,-96.546875,361.57812,-100.15625Q370.65625,-103.78125,381.96875,-103.78125Q393.01562,-103.78125,401.76562,-99.84375Q410.51562,-95.90625,415.35938,-88.625Q420.21875,-81.359375,420.21875,-72L395.60938,-72Q395.60938,-78.265625,391.78125,-81.703125Q387.95312,-85.15625,381.40625,-85.15625Q374.79688,-85.15625,370.96875,-82.234375Q367.14062,-79.3125,367.14062,-74.8125Q367.14062,-70.875,371.35938,-67.671875Q375.57812,-64.484375,386.1875,-61.0625Q396.8125,-57.65625,403.625,-53.71875Q420.21875,-44.15625,420.21875,-27.359375Q420.21875,-13.921875,410.09375,-6.25Q399.96875,1.40625,382.32812,1.40625Q369.875,1.40625,359.78125,-3.0625Q349.70312,-7.53125,344.59375,-15.296875Q339.5,-23.0625,339.5,-33.1875L364.25,-33.1875Q364.25,-24.96875,368.5,-21.0625Q372.76562,-17.15625,382.32812,-17.15625Q388.4375,-17.15625,391.98438,-19.796875Q395.54688,-22.4375,395.54688,-27.21875Z"
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_launcher_foreground.xml:40: Warning: Very long vector path (882 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      <path android:pathData="M1191.5,0Q1190.2344,-2.328125,1189.25,-6.828125Q1182.7188,1.40625,1170.9688,1.40625Q1160.2188,1.40625,1152.6875,-5.09375Q1145.1719,-11.609375,1145.1719,-21.453125Q1145.1719,-33.828125,1154.3125,-40.15625Q1163.4531,-46.484375,1180.8906,-46.484375L1188.2031,-46.484375L1188.2031,-50.484375Q1188.2031,-60.96875,1179.125,-60.96875Q1170.6875,-60.96875,1170.6875,-52.671875L1147,-52.671875Q1147,-63.703125,1156.375,-70.59375Q1165.7656,-77.484375,1180.3125,-77.484375Q1194.875,-77.484375,1203.3125,-70.375Q1211.75,-63.28125,1211.9688,-50.90625L1211.9688,-17.234375Q1212.1094,-6.75,1215.2031,-1.203125L1215.2031,0L1191.5,0ZM1176.6719,-15.46875Q1181.0938,-15.46875,1184.0156,-17.359375Q1186.9375,-19.265625,1188.2031,-21.65625L1188.2031,-33.828125L1181.3125,-33.828125Q1168.9375,-33.828125,1168.9375,-22.71875Q1168.9375,-19.484375,1171.1094,-17.46875Q1173.2969,-15.46875,1176.6719,-15.46875Z"
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_launcher_foreground.xml:44: Warning: Very long vector path (943 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      <path android:pathData="M1276.875,-38.609375Q1276.875,-50.140625,1280.6719,-59Q1284.4688,-67.859375,1291.5625,-72.671875Q1298.6719,-77.484375,1308.0938,-77.484375Q1319.9688,-77.484375,1326.6562,-69.46875L1327.5,-76.078125L1349.0156,-76.078125L1349.0156,-2.890625Q1349.0156,7.171875,1344.3281,14.59375Q1339.6562,22.015625,1330.7969,25.984375Q1321.9375,29.953125,1310.2656,29.953125Q1301.9062,29.953125,1294.0625,26.78125Q1286.2188,23.625,1282.0781,18.5625L1291.9844,4.640625Q1298.5938,12.453125,1309.4219,12.453125Q1325.1719,12.453125,1325.1719,-3.734375L1325.1719,-6.125Q1318.3594,1.40625,1307.9531,1.40625Q1294.0312,1.40625,1285.4531,-9.25Q1276.875,-19.90625,1276.875,-37.765625L1276.875,-38.609375ZM1300.6406,-37.125Q1300.6406,-27.78125,1304.1562,-22.328125Q1307.6719,-16.875,1314,-16.875Q1321.6562,-16.875,1325.1719,-22.015625L1325.1719,-54Q1321.7344,-59.203125,1314.1406,-59.203125Q1307.8125,-59.203125,1304.2188,-53.546875Q1300.6406,-47.890625,1300.6406,-37.125Z"
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "VectorPath":
   Using long vector paths is bad for performance. There are several ways to
   make the pathData shorter:
   * Using less precision
   * Removing some minor details
   * Using the Android Studio vector conversion tool
   * Rasterizing the image (converting to PNG)

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java:91: Warning: Use Integer.valueOf(matcher.group().toString()) instead [UseValueOf]
                    int intValue = new Integer(matcher.group().toString()).intValue();
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java:216: Warning: Use Integer.valueOf(matcher.group().toString()) instead [UseValueOf]
                    int intValue = new Integer(matcher.group().toString()).intValue();
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java:240: Warning: Use Integer.valueOf(matcher.group().toString()) instead [UseValueOf]
                    int intValue = new Integer(matcher.group().toString()).intValue();
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:479: Warning: Use Integer.valueOf(matcher.group().toString()) instead [UseValueOf]
                    int intValue = new Integer(matcher.group().toString()).intValue();
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java:502: Warning: Use Integer.valueOf(matcher.group().toString()) instead [UseValueOf]
                    int intValue = new Integer(matcher.group().toString()).intValue();
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseValueOf":
   You should not call the constructor for wrapper classes directly, such
   as`new Integer(42)`. Instead, call the valueOf factory method, such as
   Integer.valueOf(42). This will typically use less memory because common
   integers such as 0 and 1 will share a single instance.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:8: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:10: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:8: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~

   Explanation for issues of type "DisableBaselineAlignment":
   When a LinearLayout is used to distribute the space proportionally between
   nested layouts, the baseline alignment property should be turned off to
   make the layout computation faster.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\item_todo.xml:5: Warning: Possible overdraw: Root element paints background #ff1b1a1a with a theme that also paints a background (inferred theme is @style/Theme.AppyStoreMRecharge) [Overdraw]
    android:background="#ff1b1a1a"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\listmain.xml:3: Warning: Possible overdraw: Root element paints background #ff1b1a1a with a theme that also paints a background (inferred theme is @style/Theme.AppyStoreMRecharge) [Overdraw]
    android:background="#ff1b1a1a"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\appystoremrecharge.png: Warning: The resource R.drawable.appystoremrecharge appears to be unused [UnusedResources]
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\bg.png: Warning: The resource R.drawable.bg appears to be unused [UnusedResources]
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\bottom_navigation_menu.xml:2: Warning: The resource R.menu.bottom_navigation_menu appears to be unused [UnusedResources]
<menu xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\button_background.xml:2: Warning: The resource R.drawable.button_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\card_background.xml:2: Warning: The resource R.drawable.card_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:2: Warning: The resource R.layout.config appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\custom_dialog.xml:2: Warning: The resource R.layout.custom_dialog appears to be unused [UnusedResources]
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_launcher_background.xml:2: Warning: The resource R.drawable.ic_launcher_background appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\values\ic_launcher_background.xml:3: Warning: The resource R.color.ic_launcher_background appears to be unused [UnusedResources]
    <color name="ic_launcher_background">#00C1EC</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_launcher_foreground.xml:2: Warning: The resource R.drawable.ic_launcher_foreground appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\input_background.xml:2: Warning: The resource R.drawable.input_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\modern_toggle_off.xml:2: Warning: The resource R.drawable.modern_toggle_off appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\modern_toggle_on.xml:2: Warning: The resource R.drawable.modern_toggle_on appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\modern_toggle_selector.xml:2: Warning: The resource R.drawable.modern_toggle_selector appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\nav_item_background.xml:2: Warning: The resource R.drawable.nav_item_background appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\raw\notification.mp3: Warning: The resource R.raw.notification appears to be unused [UnusedResources]

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:89: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
            <LinearLayout
             ~~~~~~~~~~~~

   Explanation for issues of type "UselessParent":
   A layout with children that has no siblings, is not a scrollview or a root
   layout, and does not have a background, can be removed and have its
   children moved directly into the parent for a flatter and more efficient
   layout hierarchy.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-hdpi\ic_launcher.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-mdpi\ic_launcher.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-xhdpi\ic_launcher.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-xxhdpi\ic_launcher.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]

   Explanation for issues of type "IconLauncherShape":
   According to the Android Design Guide
   (https://d.android.com/r/studio-ui/designer/material/iconography) your
   launcher icons should "use a distinct silhouette", a "three-dimensional,
   front view, with a slight perspective as if viewed from above, so that
   users perceive some depth."

   The unique silhouette implies that your launcher icon should not be a
   filled square.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\appystoremrecharge.png: Warning: Found bitmap drawable res/drawable/appystoremrecharge.png in densityless folder [IconLocation]
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\bg.png: Warning: Found bitmap drawable res/drawable/bg.png in densityless folder [IconLocation]
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_stat_name.png: Warning: Found bitmap drawable res/drawable/ic_stat_name.png in densityless folder [IconLocation]
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\toggle_off.png: Warning: Found bitmap drawable res/drawable/toggle_off.png in densityless folder [IconLocation]
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\toggle_on.png: Warning: Found bitmap drawable res/drawable/toggle_on.png in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:228: Warning: This text field does not specify an inputType [TextFields]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:147: Warning: This text field does not specify an inputType [TextFields]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:184: Warning: This text field does not specify an inputType [TextFields]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:221: Warning: This text field does not specify an inputType [TextFields]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:242: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:262: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~

   Explanation for issues of type "TextFields":
   Providing an inputType attribute on a text field improves usability because
   depending on the data to be input, optimized keyboards can be shown to the
   user (such as just digits and parentheses for a phone number). 

   The lint detector also looks at the id of the view, and if the id offers a
   hint of the purpose of the field (for example, the id contains the phrase
   phone or email), then lint will also ensure that the inputType contains the
   corresponding type attributes.

   If you really want to keep the text field generic, you can suppress this
   warning by setting inputType="text".

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:228: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:248: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:107: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:127: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:147: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:167: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:187: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:147: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:184: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:221: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:242: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:262: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:81: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:123: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle:34: Warning: Use version catalog instead [UseTomlInstead]
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.3'
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle:48: Warning: Use version catalog instead (com.squareup.okhttp3:okhttp is already available as okhttp, but using version 4.12.0 instead) [UseTomlInstead]
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle:49: Warning: Use version catalog instead (com.squareup.okhttp3:logging-interceptor is already available as logging-interceptor, but using version 4.12.0 instead) [UseTomlInstead]
    implementation 'com.squareup.okhttp3:logging-interceptor:4.11.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle:52: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.annotation:annotation:1.7.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle:71: Warning: Use version catalog instead [UseTomlInstead]
    debugImplementation 'com.facebook.stetho:stetho:1.6.0'
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle:72: Warning: Use version catalog instead [UseTomlInstead]
    debugImplementation 'com.facebook.stetho:stetho-okhttp3:1.6.0'
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:42: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:75: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:107: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:139: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:176: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:22: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:43: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:64: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:28: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:54: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:79: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:23: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:45: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:67: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:93: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:271: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        this.myidt.setText("Device ID: " + getPrefid("myid", getApplicationContext()));
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java:271: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        this.myidt.setText("Device ID: " + getPrefid("myid", getApplicationContext()));
                           ~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\RecyclerviewItemAdapter.java:47: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    holder.name.setText("Waiting...");
                                        ~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\RecyclerviewItemAdapter.java:51: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    holder.name.setText("Done");
                                        ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\RecyclerviewItemAdapter.java:55: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    holder.name.setText("Processing");
                                        ~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\RecyclerviewItemAdapter.java:59: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    holder.name.setText("Failed");
                                        ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\RecyclerviewItemAdapter.java:67: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                holder.price.setText("No details available");
                                     ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:52: Warning: Hardcoded string "Home", should use @string resource [HardcodedText]
                android:text="Home"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:85: Warning: Hardcoded string "Banking", should use @string resource [HardcodedText]
                android:text="Banking"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:117: Warning: Hardcoded string "Settings", should use @string resource [HardcodedText]
                android:text="Settings"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:149: Warning: Hardcoded string "Monitor", should use @string resource [HardcodedText]
                android:text="Monitor"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:189: Warning: Hardcoded string "Automatic Mobile Recharge System", should use @string resource [HardcodedText]
                    android:text="Automatic Mobile Recharge System"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:203: Warning: Hardcoded string "Device ID: **********", should use @string resource [HardcodedText]
                    android:text="Device ID: **********"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:223: Warning: Hardcoded string "License Information", should use @string resource [HardcodedText]
                    android:text="License Information"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:236: Warning: Hardcoded string "License Key eg: xxxxx-xxxxx-xxxxx-xxxxx", should use @string resource [HardcodedText]
                    android:hint="License Key eg: xxxxx-xxxxx-xxxxx-xxxxx"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:256: Warning: Hardcoded string "PIN", should use @string resource [HardcodedText]
                        android:hint="PIN"
                        ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:259: Warning: Hardcoded string "1234", should use @string resource [HardcodedText]
                        android:text="1234"
                        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:276: Warning: Hardcoded string "V.1", should use @string resource [HardcodedText]
                            android:text="V.1"
                            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:284: Warning: Hardcoded string "V.2", should use @string resource [HardcodedText]
                            android:text="V.2"
                            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:315: Warning: Hardcoded string "Network Settings", should use @string resource [HardcodedText]
                    android:text="Network Settings"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:333: Warning: Hardcoded string "SIM A:", should use @string resource [HardcodedText]
                        android:text="SIM A:"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:359: Warning: Hardcoded string "SIM B:", should use @string resource [HardcodedText]
                        android:text="SIM B:"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:388: Warning: Hardcoded string "System Settings", should use @string resource [HardcodedText]
                    android:text="System Settings"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:407: Warning: Hardcoded string "SMS Service", should use @string resource [HardcodedText]
                        android:text="SMS Service"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:436: Warning: Hardcoded string "Accessibility", should use @string resource [HardcodedText]
                        android:text="Accessibility"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:465: Warning: Hardcoded string "Power Optimize", should use @string resource [HardcodedText]
                        android:text="Power Optimize"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:493: Warning: Hardcoded string "Find Page", should use @string resource [HardcodedText]
                        android:text="Find Page"
                        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\bottom_navigation_menu.xml:6: Warning: Hardcoded string "Home", should use @string resource [HardcodedText]
        android:title="Home" />
        ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\bottom_navigation_menu.xml:10: Warning: Hardcoded string "M Banking", should use @string resource [HardcodedText]
        android:title="M Banking" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\bottom_navigation_menu.xml:14: Warning: Hardcoded string "Settings", should use @string resource [HardcodedText]
        android:title="Settings" />
        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\bottom_navigation_menu.xml:18: Warning: Hardcoded string "Monitor", should use @string resource [HardcodedText]
        android:title="Monitor" />
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:34: Warning: Hardcoded string "Home", should use @string resource [HardcodedText]
                android:text="Home"/>
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:55: Warning: Hardcoded string "M Banking", should use @string resource [HardcodedText]
                android:text="M Banking"/>
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:76: Warning: Hardcoded string "Settings", should use @string resource [HardcodedText]
                android:text="Settings"/>
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:106: Warning: Hardcoded string "GP", should use @string resource [HardcodedText]
                    android:text="GP"/>
                    ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:116: Warning: Hardcoded string "Enter USSD", should use @string resource [HardcodedText]
                    android:hint="Enter USSD"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:126: Warning: Hardcoded string "ROBI", should use @string resource [HardcodedText]
                    android:text="ROBI"/>
                    ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:136: Warning: Hardcoded string "Enter USSD", should use @string resource [HardcodedText]
                    android:hint="Enter USSD"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:146: Warning: Hardcoded string "AIRTEL", should use @string resource [HardcodedText]
                    android:text="AIRTEL"/>
                    ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:156: Warning: Hardcoded string "Enter USSD", should use @string resource [HardcodedText]
                    android:hint="Enter USSD"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:166: Warning: Hardcoded string "BANGLALINK", should use @string resource [HardcodedText]
                    android:text="BANGLALINK"/>
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:176: Warning: Hardcoded string "Enter USSD", should use @string resource [HardcodedText]
                    android:hint="Enter USSD"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:186: Warning: Hardcoded string "TELETALK", should use @string resource [HardcodedText]
                    android:text="TELETALK"/>
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:196: Warning: Hardcoded string "Enter USSD", should use @string resource [HardcodedText]
                    android:hint="Enter USSD"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:207: Warning: Hardcoded string "Save", should use @string resource [HardcodedText]
                    android:text="Save"
                    ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\custom_dialog.xml:11: Warning: Hardcoded string " Ok ", should use @string resource [HardcodedText]
        android:text=" Ok "/>
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:40: Warning: Hardcoded string "Home", should use @string resource [HardcodedText]
                android:text="Home"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:66: Warning: Hardcoded string "M Banking", should use @string resource [HardcodedText]
                android:text="M Banking"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:91: Warning: Hardcoded string "Settings", should use @string resource [HardcodedText]
                android:text="Settings"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:104: Warning: Hardcoded string "Forward Rquest to sms", should use @string resource [HardcodedText]
        android:text="Forward Rquest to sms"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:127: Warning: Hardcoded string "Bkash", should use @string resource [HardcodedText]
                android:text="Bkash"
                ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:153: Warning: Hardcoded string "Enter number here", should use @string resource [HardcodedText]
                    android:hint="Enter number here"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:164: Warning: Hardcoded string "Rocket", should use @string resource [HardcodedText]
                android:text="Rocket"
                ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:190: Warning: Hardcoded string "Enter number here", should use @string resource [HardcodedText]
                    android:hint="Enter number here"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:201: Warning: Hardcoded string "Nogad", should use @string resource [HardcodedText]
                android:text="Nogad"
                ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:227: Warning: Hardcoded string "Enter number here", should use @string resource [HardcodedText]
                    android:hint="Enter number here"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:238: Warning: Hardcoded string "Request Every sec", should use @string resource [HardcodedText]
                android:text="Request Every sec"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:248: Warning: Hardcoded string "0 sec", should use @string resource [HardcodedText]
                android:hint="0 sec"
                ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:258: Warning: Hardcoded string "Maximum Try", should use @string resource [HardcodedText]
                android:text="Maximum Try"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:268: Warning: Hardcoded string "0", should use @string resource [HardcodedText]
                android:hint="0"
                ~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:279: Warning: Hardcoded string "Dial function", should use @string resource [HardcodedText]
                android:text="Dial function"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:299: Warning: Hardcoded string "Save", should use @string resource [HardcodedText]
                android:text="Save"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:38: Warning: Hardcoded string "License Activation", should use @string resource [HardcodedText]
                    android:text="License Activation"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:48: Warning: Hardcoded string "Enter your license key and PIN to activate", should use @string resource [HardcodedText]
                    android:text="Enter your license key and PIN to activate"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:74: Warning: Hardcoded string "License Key", should use @string resource [HardcodedText]
                        android:text="License Key"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:85: Warning: Hardcoded string "Enter your license key", should use @string resource [HardcodedText]
                        android:hint="Enter your license key"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:116: Warning: Hardcoded string "PIN", should use @string resource [HardcodedText]
                        android:text="PIN"
                        ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:127: Warning: Hardcoded string "Enter your PIN", should use @string resource [HardcodedText]
                        android:hint="Enter your PIN"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:145: Warning: Hardcoded string "Activate License", should use @string resource [HardcodedText]
                android:text="Activate License"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:160: Warning: Hardcoded string "Please enter your license key and PIN to activate", should use @string resource [HardcodedText]
                android:text="Please enter your license key and PIN to activate"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:202: Warning: Hardcoded string "🌐", should use @string resource [HardcodedText]
                                android:text="🌐"
                                ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:209: Warning: Hardcoded string "Domain Access", should use @string resource [HardcodedText]
                                android:text="Domain Access"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:221: Warning: Hardcoded string "Your domain: Loading...", should use @string resource [HardcodedText]
                            android:text="Your domain: Loading..."
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:231: Warning: Hardcoded string "🎛️ Open Dashboard", should use @string resource [HardcodedText]
                            android:text="🎛️ Open Dashboard"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:244: Warning: Hardcoded string "💡 View your license details and domain information", should use @string resource [HardcodedText]
                            android:text="💡 View your license details and domain information"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:288: Warning: Hardcoded string "📅", should use @string resource [HardcodedText]
                                android:text="📅"
                                ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:295: Warning: Hardcoded string "License Status", should use @string resource [HardcodedText]
                                android:text="License Status"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:307: Warning: Hardcoded string "License expires: Loading...", should use @string resource [HardcodedText]
                            android:text="License expires: Loading..."
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:317: Warning: Hardcoded string "Time remaining: Calculating...", should use @string resource [HardcodedText]
                            android:text="Time remaining: Calculating..."
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:337: Warning: Hardcoded string "💡 You will receive warnings 2 days before expiration", should use @string resource [HardcodedText]
                            android:text="💡 You will receive warnings 2 days before expiration"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:374: Warning: Hardcoded string "👨‍💻", should use @string resource [HardcodedText]
                android:text="👨‍💻"
                ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:381: Warning: Hardcoded string "Md Sadrul Hasan Dider", should use @string resource [HardcodedText]
                android:text="Md Sadrul Hasan Dider"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml:392: Warning: Hardcoded string "© 2025 License Activation System", should use @string resource [HardcodedText]
            android:text="© 2025 License Activation System"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\menu_main.xml:6: Warning: Hardcoded string "Mobile Banking", should use @string resource [HardcodedText]
        android:title="Mobile Banking"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\menu_main.xml:11: Warning: Hardcoded string "Forward", should use @string resource [HardcodedText]
        android:title="Forward"
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:35: Warning: Hardcoded string "Home", should use @string resource [HardcodedText]
                android:text="Home"/>
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:57: Warning: Hardcoded string "M Banking", should use @string resource [HardcodedText]
                android:text="M Banking"/>
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:79: Warning: Hardcoded string "Settings", should use @string resource [HardcodedText]
                android:text="Settings"/>
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:107: Warning: Hardcoded string "Automatic Mobile recharge system", should use @string resource [HardcodedText]
                android:text="Automatic Mobile recharge system"/>
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:122: Warning: Hardcoded string "Bkash", should use @string resource [HardcodedText]
                    android:text="Bkash"/>
                    ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:149: Warning: Hardcoded string "Rocket", should use @string resource [HardcodedText]
                    android:text="Rocket"/>
                    ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:176: Warning: Hardcoded string "Nogad", should use @string resource [HardcodedText]
                    android:text="Nogad"/>
                    ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:203: Warning: Hardcoded string "Upay", should use @string resource [HardcodedText]
                    android:text="Upay"/>
                    ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:230: Warning: Hardcoded string "Bill pay", should use @string resource [HardcodedText]
                    android:text="Bill pay"/>
                    ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:275: Warning: When you define paddingRight you should probably also define paddingLeft for right-to-left symmetry [RtlSymmetry]
                            android:paddingRight="15dp"
                            ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:283: Warning: When you define paddingRight you should probably also define paddingLeft for right-to-left symmetry [RtlSymmetry]
                            android:paddingRight="15dp"
                            ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:31: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:52: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:73: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:39: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:65: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:90: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:32: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:54: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:76: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlSymmetry":
   If you specify padding or margin on the left side of a layout, you should
   probably also specify padding on the right side (and vice versa) for
   right-to-left layout symmetry.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:275: Warning: Consider replacing android:paddingRight with android:paddingEnd="15dp" to better support right-to-left layouts [RtlHardcoded]
                            android:paddingRight="15dp"
                            ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml:283: Warning: Consider replacing android:paddingRight with android:paddingEnd="15dp" to better support right-to-left layouts [RtlHardcoded]
                            android:paddingRight="15dp"
                            ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:31: Warning: Consider replacing android:paddingLeft with android:paddingStart="5dp" to better support right-to-left layouts [RtlHardcoded]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:52: Warning: Consider replacing android:paddingLeft with android:paddingStart="5dp" to better support right-to-left layouts [RtlHardcoded]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:73: Warning: Consider replacing android:paddingLeft with android:paddingStart="5dp" to better support right-to-left layouts [RtlHardcoded]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:97: Warning: Consider replacing android:layout_alignParentLeft with android:layout_alignParentStart="true" to better support right-to-left layouts [RtlHardcoded]
                android:layout_alignParentLeft="true">
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:200: Warning: Use "end" instead of "right" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                    android:layout_gravity="right"
                                            ~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:206: Warning: Consider replacing android:layout_marginRight with android:layout_marginEnd="20dp" to better support right-to-left layouts [RtlHardcoded]
                    android:layout_marginRight="20dp"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml:208: Warning: Consider replacing android:layout_alignParentRight with android:layout_alignParentEnd="true" to better support right-to-left layouts [RtlHardcoded]
                    android:layout_alignParentRight="true"/>
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\custom_dialog.xml:10: Warning: Consider replacing android:layout_marginRight with android:layout_marginEnd="5dp" to better support right-to-left layouts [RtlHardcoded]
        android:layout_marginRight="5dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:39: Warning: Consider replacing android:paddingLeft with android:paddingStart="5dp" to better support right-to-left layouts [RtlHardcoded]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:65: Warning: Consider replacing android:paddingLeft with android:paddingStart="5dp" to better support right-to-left layouts [RtlHardcoded]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml:90: Warning: Consider replacing android:paddingLeft with android:paddingStart="5dp" to better support right-to-left layouts [RtlHardcoded]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:32: Warning: Consider replacing android:paddingLeft with android:paddingStart="5dp" to better support right-to-left layouts [RtlHardcoded]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:54: Warning: Consider replacing android:paddingLeft with android:paddingStart="5dp" to better support right-to-left layouts [RtlHardcoded]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml:76: Warning: Consider replacing android:paddingLeft with android:paddingStart="5dp" to better support right-to-left layouts [RtlHardcoded]
                android:paddingLeft="5dp"
                ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlHardcoded":
   Using Gravity#LEFT and Gravity#RIGHT can lead to problems when a layout is
   rendered in locales where text flows from right to left. Use Gravity#START
   and Gravity#END instead. Similarly, in XML gravity and layout_gravity
   attributes, use start rather than left.

   For XML attributes such as paddingLeft and layout_marginLeft, use
   paddingStart and layout_marginStart. NOTE: If your minSdkVersion is less
   than 17, you should add both the older left/right attributes as well as the
   new start/end attributes. On older platforms, where RTL is not supported
   and the start/end attributes are unknown and therefore ignored, you need
   the older left/right attributes. There is a separate lint check which
   catches that type of error.

   (Note: For Gravity#LEFT and Gravity#START, you can use these constants even
   when targeting older platforms, because the start bitmask is a superset of
   the left bitmask. Therefore, you can use gravity="start" rather than
   gravity="left|start".)

35 errors, 301 warnings
