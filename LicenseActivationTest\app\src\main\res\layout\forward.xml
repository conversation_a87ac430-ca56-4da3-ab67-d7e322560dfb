<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/activity_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bgg"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/btnSpeakContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        android:background="@color/black"
        android:paddingTop="0dp">

        <LinearLayout
            android:id="@+id/home"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/rightorder"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:background="@null"
                android:scaleType="fitCenter"
                app:tint="#E91E63"
                android:src="@drawable/ic_home_black_24dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="5dp"
                android:text="Home"
                android:textColor="@color/white"
                android:textAppearance="?android:attr/textAppearanceSmall" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/bank"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/rightorder"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:background="@null"
                app:tint="#E91E63"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_settings_cell_black_24dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="5dp"
                android:text="M Banking"
                android:textAppearance="?android:attr/textAppearanceSmall" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/sms"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/select"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:background="@null"
                app:tint="#E91E63"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_settings_black_24dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="5dp"
                android:text="Settings"
                android:textAppearance="?android:attr/textAppearanceSmall" />
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/save"
        android:gravity="center_horizontal"
        android:text="Forward Rquest to sms"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="#ffffffff" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="1dp"
        android:layout_marginRight="1dp"
        android:padding="13dp">

        <LinearLayout
            android:padding="18dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/rounded_corner"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Bkash"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="@color/white" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/bks"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:checked="false"
                    android:onClick="checkbox_clicked"
                    android:text=""
                    android:textColor="@color/white" />

                <EditText
                    android:id="@+id/bkash"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:hint="Enter number here"
                    android:textColor="@color/white"
                    android:textColorHint="@color/white"
                    android:textSize="16sp" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Rocket"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="@color/white" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/rks"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:checked="false"
                    android:onClick="checkbox_clicked"
                    android:text=""
                    android:textColor="@color/white" />

                <EditText
                    android:id="@+id/rocket"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:hint="Enter number here"
                    android:textColor="@color/white"
                    android:textColorHint="@color/white"
                    android:textSize="16sp" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Nogad"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="@color/white" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/ngs"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:checked="false"
                    android:onClick="checkbox_clicked"
                    android:text=""
                    android:textColor="@color/white" />

                <EditText
                    android:id="@+id/nogad"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:hint="Enter number here"
                    android:textColor="@color/white"
                    android:textColorHint="@color/white"
                    android:textSize="16sp" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Request Every sec"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="@color/white" />

            <EditText
                android:id="@+id/time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:hint="0 sec"
                android:textColor="@color/white"
                android:textColorHint="@color/white"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Maximum Try"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="@color/white" />

            <EditText
                android:id="@+id/trys"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:hint="0"
                android:textColor="@color/white"
                android:textColorHint="@color/white"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/blb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Dial function"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="@color/white" />

            <Spinner
                android:id="@+id/dc"
                android:layout_width="200dp"
                android:layout_height="30dp"
                android:layout_below="@+id/blb"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp" />

            <Button
                android:id="@+id/savedata"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:background="@drawable/save"
                android:text="Save"
                android:textColor="#ffffffff" />


        </LinearLayout>
    </ScrollView>
</LinearLayout>