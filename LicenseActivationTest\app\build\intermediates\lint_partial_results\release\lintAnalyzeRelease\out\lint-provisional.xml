<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="conditional_incidents">

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/util/NotificationUtils.java"
            line="90"
            column="13"
            startOffset="3426"
            endLine="90"
            endColumn="79"
            endOffset="3492"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/util/NotificationUtils.java"
            line="103"
            column="13"
            startOffset="4076"
            endLine="103"
            endColumn="77"
            endOffset="4140"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/PushService.java"
            line="172"
            column="9"
            startOffset="6917"
            endLine="172"
            endColumn="81"
            endOffset="6989"/>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Declaring a broadcastreceiver for `android.net.conn.CONNECTIVITY_CHANGE` is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use `WorkManager`.">
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="149"
            column="39"
            startOffset="7654"
            endLine="149"
            endColumn="75"
            endOffset="7690"/>
        <map>
            <condition targetGE="24"/>
        </map>
    </incident>

    <incident
        id="ForegroundServiceType"
        severity="error"
        message="To call `Service.startForeground()`, the `&lt;service>` element of manifest file must have the `foregroundServiceType` attribute specified">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/International_service.java"
            line="127"
            column="9"
            startOffset="4146"
            endLine="127"
            endColumn="24"
            endOffset="4161"/>
        <map>
            <condition targetGE="34"/>
        </map>
    </incident>

    <incident
        id="ForegroundServiceType"
        severity="error"
        message="To call `Service.startForeground()`, the `&lt;service>` element of manifest file must have the `foregroundServiceType` attribute specified">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/Recharge.java"
            line="164"
            column="9"
            startOffset="5614"
            endLine="164"
            endColumn="24"
            endOffset="5629"/>
        <map>
            <condition targetGE="34"/>
        </map>
    </incident>

    <incident
        id="ForegroundServiceType"
        severity="error"
        message="To call `Service.startForeground()`, the `&lt;service>` element of manifest file must have the `foregroundServiceType` attribute specified">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/Smsend.java"
            line="161"
            column="9"
            startOffset="5481"
            endLine="161"
            endColumn="24"
            endOffset="5496"/>
        <map>
            <condition targetGE="34"/>
        </map>
    </incident>

    <incident
        id="ForegroundServiceType"
        severity="error"
        message="To call `Service.startForeground()`, the `&lt;service>` element of manifest file must have the `foregroundServiceType` attribute specified">
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/sever.java"
            line="199"
            column="9"
            startOffset="8376"
            endLine="199"
            endColumn="24"
            endOffset="8391"/>
        <map>
            <condition targetGE="34"/>
        </map>
    </incident>

    <incident
        id="UnspecifiedRegisterReceiverFlag"
        severity="error"
        message="`smsSentReceiver` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for SMS_SENT">
        <fix-data/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/Smsend.java"
            line="204"
            column="13"
            startOffset="7171"
            endLine="204"
            endColumn="76"
            endOffset="7234"/>
        <map>
            <entry
                name="hasUnprotected"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="UnspecifiedRegisterReceiverFlag"
        severity="error"
        message="`smsDeliveredReceiver` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for SMS_DELIVERED">
        <fix-data/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/Smsend.java"
            line="207"
            column="13"
            startOffset="7297"
            endLine="207"
            endColumn="86"
            endOffset="7370"/>
        <map>
            <entry
                name="hasUnprotected"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="UnspecifiedRegisterReceiverFlag"
        severity="error"
        message="`smsSentReceiver` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for SMS_SENT">
        <fix-data/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/sever.java"
            line="728"
            column="13"
            startOffset="38587"
            endLine="728"
            endColumn="76"
            endOffset="38650"/>
        <map>
            <entry
                name="hasUnprotected"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="UnspecifiedRegisterReceiverFlag"
        severity="error"
        message="`smsDeliveredReceiver` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for SMS_DELIVERED">
        <fix-data/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*javaDir*0}/com/appystore/mrecharge/service/sever.java"
            line="729"
            column="13"
            startOffset="38664"
            endLine="729"
            endColumn="86"
            endOffset="38737"/>
        <map>
            <entry
                name="hasUnprotected"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="UsingOnClickInXml"
        severity="warning"
        message="Use databinding or explicit wiring of click listener in code">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="143"
            column="21"
            startOffset="5598"
            endLine="143"
            endColumn="55"
            endOffset="5632"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="UsingOnClickInXml"
        severity="warning"
        message="Use databinding or explicit wiring of click listener in code">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="180"
            column="21"
            startOffset="7219"
            endLine="180"
            endColumn="55"
            endOffset="7253"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="UsingOnClickInXml"
        severity="warning"
        message="Use databinding or explicit wiring of click listener in code">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="217"
            column="21"
            startOffset="8840"
            endLine="217"
            endColumn="55"
            endOffset="8874"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
                startOffset="2038"
                endOffset="2058"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*manifest*0}"
            line="37"
            column="9"
            startOffset="2038"
            endLine="37"
            endColumn="29"
            endOffset="2058"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingRight` you should probably also define `paddingLeft` for right-to-left symmetry">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="275"
            column="29"
            startOffset="11495"
            endLine="275"
            endColumn="49"
            endOffset="11515"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingRight` you should probably also define `paddingLeft` for right-to-left symmetry">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="283"
            column="29"
            startOffset="11875"
            endLine="283"
            endColumn="49"
            endOffset="11895"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="31"
            column="17"
            startOffset="1365"
            endLine="31"
            endColumn="36"
            endOffset="1384"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="52"
            column="17"
            startOffset="2293"
            endLine="52"
            endColumn="36"
            endOffset="2312"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="73"
            column="17"
            startOffset="3224"
            endLine="73"
            endColumn="36"
            endOffset="3243"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="39"
            column="17"
            startOffset="1485"
            endLine="39"
            endColumn="36"
            endOffset="1504"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="65"
            column="17"
            startOffset="2507"
            endLine="65"
            endColumn="36"
            endOffset="2526"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="90"
            column="17"
            startOffset="3474"
            endLine="90"
            endColumn="36"
            endOffset="3493"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="32"
            column="17"
            startOffset="1398"
            endLine="32"
            endColumn="36"
            endOffset="1417"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="54"
            column="17"
            startOffset="2358"
            endLine="54"
            endColumn="36"
            endOffset="2377"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="76"
            column="17"
            startOffset="3321"
            endLine="76"
            endColumn="36"
            endOffset="3340"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingRight` with `android:paddingEnd=&quot;15dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingEnd=&quot;15dp&quot;"
            oldString="paddingRight"
            replacement="paddingEnd"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="275"
            column="29"
            startOffset="11495"
            endLine="275"
            endColumn="49"
            endOffset="11515"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingRight` with `android:paddingEnd=&quot;15dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingEnd=&quot;15dp&quot;"
            oldString="paddingRight"
            replacement="paddingEnd"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="283"
            column="29"
            startOffset="11875"
            endLine="283"
            endColumn="49"
            endOffset="11895"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;5dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="31"
            column="17"
            startOffset="1365"
            endLine="31"
            endColumn="36"
            endOffset="1384"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;5dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="52"
            column="17"
            startOffset="2293"
            endLine="52"
            endColumn="36"
            endOffset="2312"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;5dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="73"
            column="17"
            startOffset="3224"
            endLine="73"
            endColumn="36"
            endOffset="3243"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_alignParentLeft` with `android:layout_alignParentStart=&quot;true&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_alignParentStart=&quot;true&quot;"
            oldString="layout_alignParentLeft"
            replacement="layout_alignParentStart"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="97"
            column="17"
            startOffset="4288"
            endLine="97"
            endColumn="47"
            endOffset="4318"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`end`&quot; instead of &quot;`right`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="200"
            column="45"
            startOffset="9528"
            endLine="200"
            endColumn="50"
            endOffset="9533"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;20dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginEnd=&quot;20dp&quot;"
            oldString="layout_marginRight"
            replacement="layout_marginEnd"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="206"
            column="21"
            startOffset="9824"
            endLine="206"
            endColumn="47"
            endOffset="9850"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_alignParentRight` with `android:layout_alignParentEnd=&quot;true&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_alignParentEnd=&quot;true&quot;"
            oldString="layout_alignParentRight"
            replacement="layout_alignParentEnd"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml"
            line="208"
            column="21"
            startOffset="9920"
            endLine="208"
            endColumn="52"
            endOffset="9951"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;5dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginEnd=&quot;5dp&quot;"
            oldString="layout_marginRight"
            replacement="layout_marginEnd"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/custom_dialog.xml"
            line="10"
            column="9"
            startOffset="386"
            endLine="10"
            endColumn="35"
            endOffset="412"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;5dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="39"
            column="17"
            startOffset="1485"
            endLine="39"
            endColumn="36"
            endOffset="1504"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;5dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="65"
            column="17"
            startOffset="2507"
            endLine="65"
            endColumn="36"
            endOffset="2526"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;5dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml"
            line="90"
            column="17"
            startOffset="3474"
            endLine="90"
            endColumn="36"
            endOffset="3493"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;5dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="32"
            column="17"
            startOffset="1398"
            endLine="32"
            endColumn="36"
            endOffset="1417"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;5dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="54"
            column="17"
            startOffset="2358"
            endLine="54"
            endColumn="36"
            endOffset="2377"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;5dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml"
            line="76"
            column="17"
            startOffset="3321"
            endLine="76"
            endColumn="36"
            endOffset="3340"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

</incidents>
