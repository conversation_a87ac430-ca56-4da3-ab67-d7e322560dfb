<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.10.1">

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `com.google.firebase.auth.api.fallback.service.FirebaseAuthFallbackService`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;com.google.firebase.auth.api.fallback.service.FirebaseAuthFallbackService&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="215"
            column="27"/>
    </issue>

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `com.google.android.gms.measurement.AppMeasurementReceiver`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;com.google.android.gms.measurement.AppMeasurementReceiver&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="280"
            column="27"/>
    </issue>

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `com.google.android.gms.measurement.AppMeasurementService`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;com.google.android.gms.measurement.AppMeasurementService&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="284"
            column="27"/>
    </issue>

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `com.google.android.gms.measurement.AppMeasurementJobService`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;com.google.android.gms.measurement.AppMeasurementJobService&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="288"
            column="27"/>
    </issue>

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `androidx.room.MultiInstanceInvalidationService`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;androidx.room.MultiInstanceInvalidationService&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="296"
            column="27"/>
    </issue>

    <issue
        id="ScrollViewSize"
        severity="Warning"
        message="This LinearLayout should use `android:layout_height=&quot;wrap_content&quot;`"
        category="Correctness"
        priority="7"
        summary="ScrollView size validation"
        explanation="ScrollView children must set their `layout_width` or `layout_height` attributes to `wrap_content` rather than `fill_parent` or `match_parent` in the scrolling dimension."
        errorLine1="            android:layout_height=&quot;match_parent&quot;>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="88"
            column="13"/>
    </issue>

    <issue
        id="ScrollViewSize"
        severity="Warning"
        message="This LinearLayout should use `android:layout_height=&quot;wrap_content&quot;`"
        category="Correctness"
        priority="7"
        summary="ScrollView size validation"
        explanation="ScrollView children must set their `layout_width` or `layout_height` attributes to `wrap_content` rather than `fill_parent` or `match_parent` in the scrolling dimension."
        errorLine1="            android:layout_height=&quot;match_parent&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="118"
            column="13"/>
    </issue>

    <issue
        id="ScrollViewSize"
        severity="Warning"
        message="This LinearLayout should use `android:layout_height=&quot;wrap_content&quot;`"
        category="Correctness"
        priority="7"
        summary="ScrollView size validation"
        explanation="ScrollView children must set their `layout_width` or `layout_height` attributes to `wrap_content` rather than `fill_parent` or `match_parent` in the scrolling dimension."
        errorLine1="            android:layout_height=&quot;match_parent&quot;>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="92"
            column="13"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="        edit.commit();"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="497"
            column="14"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="                edit.commit();"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java"
            line="61"
            column="22"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="        edit.commit();"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java"
            line="156"
            column="14"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="                        edit.commit();"
        errorLine2="                             ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java"
            line="179"
            column="30"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="                edit.commit();"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="341"
            column="22"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="                edit.commit();"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="374"
            column="22"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="                edit.commit();"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="412"
            column="22"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="                edit.commit();"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="422"
            column="22"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="                edit.commit();"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="475"
            column="22"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="        edit.commit();"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="608"
            column="14"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="        edit.commit();"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Monitoring.java"
            line="97"
            column="14"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="        edit.commit();"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java"
            line="303"
            column="14"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="        edit.commit();"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="532"
            column="14"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="        PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit().remove(str).commit();"
        errorLine2="                                                                                                  ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="536"
            column="99"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="                edit.commit();"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\intsetting.java"
            line="83"
            column="22"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="                edit.commit();"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\intsetting.java"
            line="101"
            column="22"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="                edit.commit();"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\intsetting.java"
            line="119"
            column="22"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="                edit.commit();"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\intsetting.java"
            line="137"
            column="22"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="                edit.commit();"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\intsetting.java"
            line="155"
            column="22"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="        edit.commit();"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\intsetting.java"
            line="181"
            column="14"/>
    </issue>

    <issue
        id="CutPasteId"
        severity="Warning"
        message="The id `R.id.licensekey` has already been looked up in this method; possible cut &amp; paste error?"
        category="Correctness"
        priority="6"
        summary="Likely cut &amp; paste mistakes"
        explanation="This lint check looks for cases where you have cut &amp; pasted calls to `findViewById` but have forgotten to update the R.id field. It&apos;s possible that your code is simply (redundantly) looking up the field repeatedly, but lint cannot distinguish that from a case where you for example want to initialize fields `prev` and `next` and you cut &amp; pasted `findViewById(R.id.prev)` and forgot to update the second initialization to `R.id.next`."
        errorLine1="        this.url = (EditText) findViewById(R.id.licensekey);"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="265"
            column="31"/>
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="158"
            column="35"
            message="First usage here"/>
    </issue>

    <issue
        id="CutPasteId"
        severity="Warning"
        message="The id `R.id.dpin` has already been looked up in this method; possible cut &amp; paste error?"
        category="Correctness"
        priority="6"
        summary="Likely cut &amp; paste mistakes"
        explanation="This lint check looks for cases where you have cut &amp; pasted calls to `findViewById` but have forgotten to update the R.id field. It&apos;s possible that your code is simply (redundantly) looking up the field repeatedly, but lint cannot distinguish that from a case where you for example want to initialize fields `prev` and `next` and you cut &amp; pasted `findViewById(R.id.prev)` and forgot to update the second initialization to `R.id.next`."
        errorLine1="        this.mpin = (EditText) findViewById(R.id.dpin);"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="266"
            column="32"/>
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="159"
            column="36"
            message="First usage here"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        String code = String.format(&quot;%010d&quot;, combined);"
        errorLine2="                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="595"
            column="23"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            } else if (&quot;30D @598Tk (C: 90)\n4. Free 4G SIM at 63Tk Recharge \n5. Flash Drive 510mins, 30D@TK307 (C:45)\n p. Prev \n#. Next&quot;.toLowerCase().indexOf(&quot;next&quot;) >= 0) {"
        errorLine2="                                                                                                                                            ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java"
            line="44"
            column="141"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return (str == null || str.length() == 0) ? &quot;&quot; : str.toLowerCase();"
        errorLine2="                                                             ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java"
            line="77"
            column="62"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        String tkfindfinall = tkfindfinall(readLine.toLowerCase());"
        errorLine2="                                                                    ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java"
            line="95"
            column="69"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                            System.out.println(&quot;goo &quot; + readLine.toLowerCase().indexOf(&quot;main&quot;));"
        errorLine2="                                                                 ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java"
            line="98"
            column="66"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        if (readLine.toLowerCase().indexOf(&quot;tk &quot; + intValue) >= 0) {"
        errorLine2="                                     ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java"
            line="218"
            column="38"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        if (readLine.toLowerCase().indexOf(intValue + &quot;tk&quot;) >= 0) {"
        errorLine2="                                     ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java"
            line="242"
            column="38"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return !TextUtils.isEmpty(str2) ? str2.toLowerCase() : &quot;osman&quot;;"
        errorLine2="                                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java"
            line="275"
            column="48"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;ussd&quot;) ||"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="156"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;balance&quot;) ||"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="157"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;recharge&quot;) ||"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="158"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;tk&quot;) ||"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="159"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;taka&quot;) ||"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="160"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;successful&quot;) ||"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="161"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;failed&quot;))) {"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="162"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        String lowerResponse = response.toLowerCase();"
        errorLine2="                                        ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="246"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                if (text != null &amp;&amp; text.toString().toLowerCase().contains(buttonText.toLowerCase())) {"
        errorLine2="                                                    ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="314"
            column="53"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                if (text != null &amp;&amp; text.toString().toLowerCase().contains(buttonText.toLowerCase())) {"
        errorLine2="                                                                                      ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="314"
            column="87"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        String tkfindfinall = tkfindfinall(readLine.toLowerCase());"
        errorLine2="                                                                    ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="358"
            column="69"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    if (text.toString().toLowerCase().equals(&quot;ok&quot;) || text.toString().toLowerCase().equals(&quot;cancel&quot;)) {"
        errorLine2="                                        ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="389"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    if (text.toString().toLowerCase().equals(&quot;ok&quot;) || text.toString().toLowerCase().equals(&quot;cancel&quot;)) {"
        errorLine2="                                                                                      ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="389"
            column="87"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        } else if (text2 != null &amp;&amp; child2.getClassName().equals(Button.class.getName()) &amp;&amp; (text2.toString().toLowerCase().equals(&quot;ok&quot;) || text2.toString().toLowerCase().equals(&quot;cancel&quot;))) {"
        errorLine2="                                                                                                                              ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="405"
            column="127"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        } else if (text2 != null &amp;&amp; child2.getClassName().equals(Button.class.getName()) &amp;&amp; (text2.toString().toLowerCase().equals(&quot;ok&quot;) || text2.toString().toLowerCase().equals(&quot;cancel&quot;))) {"
        errorLine2="                                                                                                                                                                             ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="405"
            column="174"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        if (readLine.toLowerCase().indexOf(intValue + &quot;tk&quot;) >= 0) {"
        errorLine2="                                     ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="481"
            column="38"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        if (readLine.toLowerCase().indexOf(&quot;tk &quot; + intValue) >= 0) {"
        errorLine2="                                     ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="504"
            column="38"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return (str == null || str.length() == 0) ? &quot;&quot; : str.toLowerCase();"
        errorLine2="                                                             ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="514"
            column="62"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return !TextUtils.isEmpty(tkfind2) ? tkfind2.toLowerCase() : &quot;osman&quot;;"
        errorLine2="                                                     ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="562"
            column="54"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                i = cursor.getInt(cursor.getColumnIndex(&quot;resend&quot;));"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="210"
            column="35"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                str5 = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_NUMBER));"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="229"
            column="41"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                str3 = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_AMOUNT));"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="230"
            column="41"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                str4 = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_TIME));"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="231"
            column="41"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                this.number = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_NUMBER));"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="319"
            column="48"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                this.operator = cursor.getString(cursor.getColumnIndex(&quot;operator&quot;));"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="320"
            column="50"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                this.amount = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_AMOUNT));"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="321"
            column="48"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                this.extra = cursor.getString(cursor.getColumnIndex(&quot;extra&quot;));"
        errorLine2="                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="322"
            column="47"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                this.id = cursor.getInt(cursor.getColumnIndex(CONTACTS_COLUMN_ID));"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="323"
            column="41"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                i = cursor.getInt(cursor.getColumnIndex(&quot;pos&quot;));"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="436"
            column="35"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                i = cursor.getInt(cursor.getColumnIndex(&quot;sim&quot;));"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="453"
            column="35"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                i2 = Integer.parseInt(data.getString(data.getColumnIndex(DbHelper.CONTACTS_COLUMN_ID)));"
        errorLine2="                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java"
            line="49"
            column="54"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="            int i4 = data.getInt(data.getColumnIndex(&quot;status&quot;));"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java"
            line="55"
            column="34"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                String orderId = cursor.getString(cursor.getColumnIndex(&quot;orderid&quot;));"
        errorLine2="                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Recharge.java"
            line="83"
            column="51"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                String ussdCode = cursor.getString(cursor.getColumnIndex(&quot;ussd&quot;));"
        errorLine2="                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Recharge.java"
            line="84"
            column="52"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                String slotStr = cursor.getString(cursor.getColumnIndex(&quot;slot&quot;));"
        errorLine2="                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Recharge.java"
            line="85"
            column="51"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                String id = cursor.getString(cursor.getColumnIndex(DbHelper.CONTACTS_COLUMN_ID));"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Recharge.java"
            line="86"
            column="46"/>
    </issue>

    <issue
        id="ShowToast"
        severity="Warning"
        message="Expected duration `Toast.LENGTH_SHORT` or `Toast.LENGTH_LONG`, a custom duration value is not supported"
        category="Correctness"
        priority="6"
        summary="Toast created but not shown"
        explanation="`Toast.makeText()` creates a `Toast` but does **not** show it. You must call `show()` on the resulting object to actually make the `Toast` appear."
        errorLine1="            Toast.makeText(this.appContext, &quot;Error on function 1&quot;, 0).show();"
        errorLine2="                                                                   ~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java"
            line="117"
            column="68"/>
    </issue>

    <issue
        id="SimpleDateFormat"
        severity="Warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates."
        category="Correctness"
        priority="6"
        summary="Implied locale in date format"
        explanation="Almost all callers should use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()` to get a ready-made instance of SimpleDateFormat suitable for the user&apos;s locale. The main reason you&apos;d create an instance this class directly is because you need to format/parse a specific machine-readable format, in which case you almost certainly want to explicitly ask for US to ensure that you get ASCII digits (rather than, say, Arabic digits).&#xA;&#xA;Therefore, you should either use the form of the SimpleDateFormat constructor where you pass in an explicit locale, such as Locale.US, or use one of the get instance methods, or suppress this error if really know what you are doing."
        url="https://developer.android.com/reference/java/text/SimpleDateFormat.html"
        urls="https://developer.android.com/reference/java/text/SimpleDateFormat.html"
        errorLine1="            SimpleDateFormat format = new SimpleDateFormat(&quot;yyyy-MM-dd HH:mm:ss&quot;);"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\util\NotificationUtils.java"
            line="172"
            column="39"/>
    </issue>

    <issue
        id="WrongConstant"
        severity="Error"
        message="Must be one of: Context.POWER_SERVICE, Context.WINDOW_SERVICE, Context.LAYOUT_INFLATER_SERVICE, Context.ACCOUNT_SERVICE, Context.ACTIVITY_SERVICE, Context.ALARM_SERVICE, Context.NOTIFICATION_SERVICE, Context.ACCESSIBILITY_SERVICE, Context.CAPTIONING_SERVICE, Context.KEYGUARD_SERVICE, Context.LOCATION_SERVICE, Context.HEALTHCONNECT_SERVICE, Context.SEARCH_SERVICE, Context.SENSOR_SERVICE, Context.STORAGE_SERVICE, Context.STORAGE_STATS_SERVICE, Context.WALLPAPER_SERVICE, Context.VIBRATOR_MANAGER_SERVICE, Context.VIBRATOR_SERVICE, Context.CONNECTIVITY_SERVICE, Context.IPSEC_SERVICE, Context.VPN_MANAGEMENT_SERVICE, Context.NETWORK_STATS_SERVICE, Context.WIFI_SERVICE, Context.WIFI_AWARE_SERVICE, Context.WIFI_P2P_SERVICE, Context.WIFI_RTT_RANGING_SERVICE, Context.NSD_SERVICE, Context.AUDIO_SERVICE, Context.FINGERPRINT_SERVICE, Context.BIOMETRIC_SERVICE, Context.MEDIA_ROUTER_SERVICE, Context.TELEPHONY_SERVICE, Context.TELEPHONY_SUBSCRIPTION_SERVICE, Context.CARRIER_CONFIG_SERVICE, Context.EUICC_SERVICE, Context.TELECOM_SERVICE, Context.CLIPBOARD_SERVICE, Context.INPUT_METHOD_SERVICE, Context.TEXT_SERVICES_MANAGER_SERVICE, Context.TEXT_CLASSIFICATION_SERVICE, Context.APPWIDGET_SERVICE, Context.DROPBOX_SERVICE, Context.DEVICE_POLICY_SERVICE, Context.UI_MODE_SERVICE, Context.DOWNLOAD_SERVICE, Context.NFC_SERVICE, Context.BLUETOOTH_SERVICE, Context.USB_SERVICE, Context.LAUNCHER_APPS_SERVICE, Context.INPUT_SERVICE, Context.DISPLAY_SERVICE, Context.USER_SERVICE, Context.RESTRICTIONS_SERVICE, Context.APP_OPS_SERVICE, Context.ROLE_SERVICE, Context.CAMERA_SERVICE, Context.PRINT_SERVICE, Context.CONSUMER_IR_SERVICE, Context.TV_INTERACTIVE_APP_SERVICE, Context.TV_INPUT_SERVICE, Context.USAGE_STATS_SERVICE, Context.MEDIA_SESSION_SERVICE, Context.MEDIA_COMMUNICATION_SERVICE, Context.BATTERY_SERVICE, Context.JOB_SCHEDULER_SERVICE, Context.PERSISTENT_DATA_BLOCK_SERVICE, Context.MEDIA_PROJECTION_SERVICE, Context.MIDI_SERVICE, Context.HARDWARE_PROPERTIES_SERVICE, Context.SHORTCUT_SERVICE, Context.SYSTEM_HEALTH_SERVICE, Context.COMPANION_DEVICE_SERVICE, Context.VIRTUAL_DEVICE_SERVICE, Context.CROSS_PROFILE_APPS_SERVICE, Context.LOCALE_SERVICE, Context.MEDIA_METRICS_SERVICE, Context.DISPLAY_HASH_SERVICE, Context.CREDENTIAL_SERVICE, Context.DEVICE_LOCK_SERVICE, Context.GRAMMATICAL_INFLECTION_SERVICE, Context.SECURITY_STATE_SERVICE, Context.CONTACT_KEYS_SERVICE"
        category="Correctness"
        priority="6"
        summary="Incorrect constant"
        explanation="Ensures that when parameter in a method only allows a specific set of constants, calls obey those rules."
        errorLine1="        NetworkInfo activeNetworkInfo = ((ConnectivityManager) context.getSystemService(&quot;connectivity&quot;)).getActiveNetworkInfo();"
        errorLine2="                                                                                        ~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="470"
            column="89"/>
    </issue>

    <issue
        id="WrongConstant"
        severity="Error"
        message="Must be one of: Toast.LENGTH_SHORT, Toast.LENGTH_LONG"
        category="Correctness"
        priority="6"
        summary="Incorrect constant"
        explanation="Ensures that when parameter in a method only allows a specific set of constants, calls obey those rules."
        errorLine1="            Toast.makeText(this.appContext, &quot;Error on function 1&quot;, 0).show();"
        errorLine2="                                                                   ~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java"
            line="117"
            column="68"/>
    </issue>

    <issue
        id="WrongConstant"
        severity="Error"
        message="Must be one of: Context.POWER_SERVICE, Context.WINDOW_SERVICE, Context.LAYOUT_INFLATER_SERVICE, Context.ACCOUNT_SERVICE, Context.ACTIVITY_SERVICE, Context.ALARM_SERVICE, Context.NOTIFICATION_SERVICE, Context.ACCESSIBILITY_SERVICE, Context.CAPTIONING_SERVICE, Context.KEYGUARD_SERVICE, Context.LOCATION_SERVICE, Context.HEALTHCONNECT_SERVICE, Context.SEARCH_SERVICE, Context.SENSOR_SERVICE, Context.STORAGE_SERVICE, Context.STORAGE_STATS_SERVICE, Context.WALLPAPER_SERVICE, Context.VIBRATOR_MANAGER_SERVICE, Context.VIBRATOR_SERVICE, Context.CONNECTIVITY_SERVICE, Context.IPSEC_SERVICE, Context.VPN_MANAGEMENT_SERVICE, Context.NETWORK_STATS_SERVICE, Context.WIFI_SERVICE, Context.WIFI_AWARE_SERVICE, Context.WIFI_P2P_SERVICE, Context.WIFI_RTT_RANGING_SERVICE, Context.NSD_SERVICE, Context.AUDIO_SERVICE, Context.FINGERPRINT_SERVICE, Context.BIOMETRIC_SERVICE, Context.MEDIA_ROUTER_SERVICE, Context.TELEPHONY_SERVICE, Context.TELEPHONY_SUBSCRIPTION_SERVICE, Context.CARRIER_CONFIG_SERVICE, Context.EUICC_SERVICE, Context.TELECOM_SERVICE, Context.CLIPBOARD_SERVICE, Context.INPUT_METHOD_SERVICE, Context.TEXT_SERVICES_MANAGER_SERVICE, Context.TEXT_CLASSIFICATION_SERVICE, Context.APPWIDGET_SERVICE, Context.DROPBOX_SERVICE, Context.DEVICE_POLICY_SERVICE, Context.UI_MODE_SERVICE, Context.DOWNLOAD_SERVICE, Context.NFC_SERVICE, Context.BLUETOOTH_SERVICE, Context.USB_SERVICE, Context.LAUNCHER_APPS_SERVICE, Context.INPUT_SERVICE, Context.DISPLAY_SERVICE, Context.USER_SERVICE, Context.RESTRICTIONS_SERVICE, Context.APP_OPS_SERVICE, Context.ROLE_SERVICE, Context.CAMERA_SERVICE, Context.PRINT_SERVICE, Context.CONSUMER_IR_SERVICE, Context.TV_INTERACTIVE_APP_SERVICE, Context.TV_INPUT_SERVICE, Context.USAGE_STATS_SERVICE, Context.MEDIA_SESSION_SERVICE, Context.MEDIA_COMMUNICATION_SERVICE, Context.BATTERY_SERVICE, Context.JOB_SCHEDULER_SERVICE, Context.PERSISTENT_DATA_BLOCK_SERVICE, Context.MEDIA_PROJECTION_SERVICE, Context.MIDI_SERVICE, Context.HARDWARE_PROPERTIES_SERVICE, Context.SHORTCUT_SERVICE, Context.SYSTEM_HEALTH_SERVICE, Context.COMPANION_DEVICE_SERVICE, Context.VIRTUAL_DEVICE_SERVICE, Context.CROSS_PROFILE_APPS_SERVICE, Context.LOCALE_SERVICE, Context.MEDIA_METRICS_SERVICE, Context.DISPLAY_HASH_SERVICE, Context.CREDENTIAL_SERVICE, Context.DEVICE_LOCK_SERVICE, Context.GRAMMATICAL_INFLECTION_SERVICE, Context.SECURITY_STATE_SERVICE, Context.CONTACT_KEYS_SERVICE"
        category="Correctness"
        priority="6"
        summary="Incorrect constant"
        explanation="Ensures that when parameter in a method only allows a specific set of constants, calls obey those rules."
        errorLine1="        Iterator&lt;ActivityManager.RunningServiceInfo> it = ((ActivityManager) getSystemService(&quot;activity&quot;)).getRunningServices(Integer.MAX_VALUE).iterator();"
        errorLine2="                                                                                              ~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="612"
            column="95"/>
    </issue>

    <issue
        id="WrongConstant"
        severity="Error"
        message="Must be one of: Context.POWER_SERVICE, Context.WINDOW_SERVICE, Context.LAYOUT_INFLATER_SERVICE, Context.ACCOUNT_SERVICE, Context.ACTIVITY_SERVICE, Context.ALARM_SERVICE, Context.NOTIFICATION_SERVICE, Context.ACCESSIBILITY_SERVICE, Context.CAPTIONING_SERVICE, Context.KEYGUARD_SERVICE, Context.LOCATION_SERVICE, Context.HEALTHCONNECT_SERVICE, Context.SEARCH_SERVICE, Context.SENSOR_SERVICE, Context.STORAGE_SERVICE, Context.STORAGE_STATS_SERVICE, Context.WALLPAPER_SERVICE, Context.VIBRATOR_MANAGER_SERVICE, Context.VIBRATOR_SERVICE, Context.CONNECTIVITY_SERVICE, Context.IPSEC_SERVICE, Context.VPN_MANAGEMENT_SERVICE, Context.NETWORK_STATS_SERVICE, Context.WIFI_SERVICE, Context.WIFI_AWARE_SERVICE, Context.WIFI_P2P_SERVICE, Context.WIFI_RTT_RANGING_SERVICE, Context.NSD_SERVICE, Context.AUDIO_SERVICE, Context.FINGERPRINT_SERVICE, Context.BIOMETRIC_SERVICE, Context.MEDIA_ROUTER_SERVICE, Context.TELEPHONY_SERVICE, Context.TELEPHONY_SUBSCRIPTION_SERVICE, Context.CARRIER_CONFIG_SERVICE, Context.EUICC_SERVICE, Context.TELECOM_SERVICE, Context.CLIPBOARD_SERVICE, Context.INPUT_METHOD_SERVICE, Context.TEXT_SERVICES_MANAGER_SERVICE, Context.TEXT_CLASSIFICATION_SERVICE, Context.APPWIDGET_SERVICE, Context.DROPBOX_SERVICE, Context.DEVICE_POLICY_SERVICE, Context.UI_MODE_SERVICE, Context.DOWNLOAD_SERVICE, Context.NFC_SERVICE, Context.BLUETOOTH_SERVICE, Context.USB_SERVICE, Context.LAUNCHER_APPS_SERVICE, Context.INPUT_SERVICE, Context.DISPLAY_SERVICE, Context.USER_SERVICE, Context.RESTRICTIONS_SERVICE, Context.APP_OPS_SERVICE, Context.ROLE_SERVICE, Context.CAMERA_SERVICE, Context.PRINT_SERVICE, Context.CONSUMER_IR_SERVICE, Context.TV_INTERACTIVE_APP_SERVICE, Context.TV_INPUT_SERVICE, Context.USAGE_STATS_SERVICE, Context.MEDIA_SESSION_SERVICE, Context.MEDIA_COMMUNICATION_SERVICE, Context.BATTERY_SERVICE, Context.JOB_SCHEDULER_SERVICE, Context.PERSISTENT_DATA_BLOCK_SERVICE, Context.MEDIA_PROJECTION_SERVICE, Context.MIDI_SERVICE, Context.HARDWARE_PROPERTIES_SERVICE, Context.SHORTCUT_SERVICE, Context.SYSTEM_HEALTH_SERVICE, Context.COMPANION_DEVICE_SERVICE, Context.VIRTUAL_DEVICE_SERVICE, Context.CROSS_PROFILE_APPS_SERVICE, Context.LOCALE_SERVICE, Context.MEDIA_METRICS_SERVICE, Context.DISPLAY_HASH_SERVICE, Context.CREDENTIAL_SERVICE, Context.DEVICE_LOCK_SERVICE, Context.GRAMMATICAL_INFLECTION_SERVICE, Context.SECURITY_STATE_SERVICE, Context.CONTACT_KEYS_SERVICE"
        category="Correctness"
        priority="6"
        summary="Incorrect constant"
        explanation="Ensures that when parameter in a method only allows a specific set of constants, calls obey those rules."
        errorLine1="        Iterator&lt;AccessibilityServiceInfo> it = ((AccessibilityManager) context.getSystemService(&quot;accessibility&quot;)).getEnabledAccessibilityServiceList(-1).iterator();"
        errorLine2="                                                                                                 ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="650"
            column="98"/>
    </issue>

    <issue
        id="WrongConstant"
        severity="Error"
        message="Must be one or more of: AccessibilityServiceInfo.FEEDBACK_AUDIBLE, AccessibilityServiceInfo.FEEDBACK_GENERIC, AccessibilityServiceInfo.FEEDBACK_HAPTIC, AccessibilityServiceInfo.FEEDBACK_SPOKEN, AccessibilityServiceInfo.FEEDBACK_VISUAL, AccessibilityServiceInfo.FEEDBACK_BRAILLE"
        category="Correctness"
        priority="6"
        summary="Incorrect constant"
        explanation="Ensures that when parameter in a method only allows a specific set of constants, calls obey those rules."
        errorLine1="        accessibilityServiceInfo.feedbackType = 16;"
        errorLine2="                                                ~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="445"
            column="49"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Declaring a broadcastreceiver for `android.net.conn.CONNECTIVITY_CHANGE` is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use `WorkManager`."
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="                &lt;action android:name=&quot;android.net.conn.CONNECTIVITY_CHANGE&quot;/>"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="149"
            column="39"/>
    </issue>

    <issue
        id="ForegroundServiceType"
        severity="Error"
        message="To call `Service.startForeground()`, the `&lt;service>` element of manifest file must have the `foregroundServiceType` attribute specified"
        category="Correctness"
        priority="5"
        summary="Missing `foregroundServiceType` attribute in manifest"
        explanation="For `targetSdkVersion` >= 34, to call `Service.startForeground()`, the &lt;service> element in the manifest file must have the `foregroundServiceType` attribute specified."
        errorLine1="        startForeground(NOTIFICATION_ID, builder.build());"
        errorLine2="        ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\International_service.java"
            line="127"
            column="9"/>
    </issue>

    <issue
        id="ForegroundServiceType"
        severity="Error"
        message="To call `Service.startForeground()`, the `&lt;service>` element of manifest file must have the `foregroundServiceType` attribute specified"
        category="Correctness"
        priority="5"
        summary="Missing `foregroundServiceType` attribute in manifest"
        explanation="For `targetSdkVersion` >= 34, to call `Service.startForeground()`, the &lt;service> element in the manifest file must have the `foregroundServiceType` attribute specified."
        errorLine1="        startForeground(NOTIFICATION_ID, builder.build());"
        errorLine2="        ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Recharge.java"
            line="164"
            column="9"/>
    </issue>

    <issue
        id="ForegroundServiceType"
        severity="Error"
        message="To call `Service.startForeground()`, the `&lt;service>` element of manifest file must have the `foregroundServiceType` attribute specified"
        category="Correctness"
        priority="5"
        summary="Missing `foregroundServiceType` attribute in manifest"
        explanation="For `targetSdkVersion` >= 34, to call `Service.startForeground()`, the &lt;service> element in the manifest file must have the `foregroundServiceType` attribute specified."
        errorLine1="        startForeground(NOTIFICATION_ID, builder.build());"
        errorLine2="        ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Smsend.java"
            line="161"
            column="9"/>
    </issue>

    <issue
        id="ForegroundServiceType"
        severity="Error"
        message="To call `Service.startForeground()`, the `&lt;service>` element of manifest file must have the `foregroundServiceType` attribute specified"
        category="Correctness"
        priority="5"
        summary="Missing `foregroundServiceType` attribute in manifest"
        explanation="For `targetSdkVersion` >= 34, to call `Service.startForeground()`, the &lt;service> element in the manifest file must have the `foregroundServiceType` attribute specified."
        errorLine1="        startForeground(1, notificationBuilder.build());"
        errorLine2="        ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\sever.java"
            line="199"
            column="9"/>
    </issue>

    <issue
        id="RedundantLabel"
        severity="Warning"
        message="Redundant label can be removed"
        category="Correctness"
        priority="5"
        summary="Redundant label on activity"
        explanation="When an activity does not have a label attribute, it will use the one from the application tag. Since the application has already specified the same label, the label on this activity can be omitted."
        errorLine1="            android:label=&quot;@string/app_name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="43"
            column="13"/>
    </issue>

    <issue
        id="UnspecifiedRegisterReceiverFlag"
        severity="Error"
        message="`smsSentReceiver` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for SMS_SENT"
        category="Correctness"
        priority="5"
        summary="Missing `registerReceiver()` exported flag"
        explanation="In Android U, all receivers registering for non-system broadcasts are required to include a flag indicating the receiver&apos;s exported state. Apps registering for non-system broadcasts should use the `ContextCompat#registerReceiver` APIs with flags set to either `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED`.&#xA;&#xA;If you are not expecting broadcasts from other apps on the device, register your receiver with `RECEIVER_NOT_EXPORTED` to protect your receiver on all platform releases."
        url="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)"
        urls="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)"
        errorLine1="            registerReceiver(smsSentReceiver, new IntentFilter(&quot;SMS_SENT&quot;));"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Smsend.java"
            line="204"
            column="13"/>
    </issue>

    <issue
        id="UnspecifiedRegisterReceiverFlag"
        severity="Error"
        message="`smsDeliveredReceiver` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for SMS_DELIVERED"
        category="Correctness"
        priority="5"
        summary="Missing `registerReceiver()` exported flag"
        explanation="In Android U, all receivers registering for non-system broadcasts are required to include a flag indicating the receiver&apos;s exported state. Apps registering for non-system broadcasts should use the `ContextCompat#registerReceiver` APIs with flags set to either `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED`.&#xA;&#xA;If you are not expecting broadcasts from other apps on the device, register your receiver with `RECEIVER_NOT_EXPORTED` to protect your receiver on all platform releases."
        url="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)"
        urls="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)"
        errorLine1="            registerReceiver(smsDeliveredReceiver, new IntentFilter(&quot;SMS_DELIVERED&quot;));"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Smsend.java"
            line="207"
            column="13"/>
    </issue>

    <issue
        id="UnspecifiedRegisterReceiverFlag"
        severity="Error"
        message="`smsSentReceiver` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for SMS_SENT"
        category="Correctness"
        priority="5"
        summary="Missing `registerReceiver()` exported flag"
        explanation="In Android U, all receivers registering for non-system broadcasts are required to include a flag indicating the receiver&apos;s exported state. Apps registering for non-system broadcasts should use the `ContextCompat#registerReceiver` APIs with flags set to either `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED`.&#xA;&#xA;If you are not expecting broadcasts from other apps on the device, register your receiver with `RECEIVER_NOT_EXPORTED` to protect your receiver on all platform releases."
        url="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)"
        urls="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)"
        errorLine1="            registerReceiver(smsSentReceiver, new IntentFilter(&quot;SMS_SENT&quot;));"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\sever.java"
            line="728"
            column="13"/>
    </issue>

    <issue
        id="UnspecifiedRegisterReceiverFlag"
        severity="Error"
        message="`smsDeliveredReceiver` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for SMS_DELIVERED"
        category="Correctness"
        priority="5"
        summary="Missing `registerReceiver()` exported flag"
        explanation="In Android U, all receivers registering for non-system broadcasts are required to include a flag indicating the receiver&apos;s exported state. Apps registering for non-system broadcasts should use the `ContextCompat#registerReceiver` APIs with flags set to either `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED`.&#xA;&#xA;If you are not expecting broadcasts from other apps on the device, register your receiver with `RECEIVER_NOT_EXPORTED` to protect your receiver on all platform releases."
        url="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)"
        urls="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)"
        errorLine1="            registerReceiver(smsDeliveredReceiver, new IntentFilter(&quot;SMS_DELIVERED&quot;));"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\sever.java"
            line="729"
            column="13"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.10.1 is available: 8.11.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.10.1&quot;"
        errorLine2="      ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.android.tools:desugar_jdk_libs than 2.1.3 is available: 2.1.5"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    coreLibraryDesugaring &apos;com.android.tools:desugar_jdk_libs:2.1.3&apos;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle"
            line="34"
            column="27"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.squareup.okhttp3:okhttp than 4.11.0 is available: 4.12.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;com.squareup.okhttp3:okhttp:4.11.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle"
            line="48"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.squareup.okhttp3:logging-interceptor than 4.11.0 is available: 4.12.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;com.squareup.okhttp3:logging-interceptor:4.11.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle"
            line="49"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.annotation:annotation than 1.7.1 is available: 1.9.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.annotation:annotation:1.7.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle"
            line="52"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="appcompat = &quot;1.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.firebase:firebase-messaging than 24.0.0 is available: 24.1.2"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="firebase-messaging = &quot;24.0.0&quot;"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml"
            line="13"
            column="22"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.8.0 is available: 33.16.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="firebase-bom = &quot;32.8.0&quot;"
        errorLine2="               ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml"
            line="14"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.android.gms:play-services-base than 18.3.0 is available: 18.7.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="play-services-base = &quot;18.3.0&quot;"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml"
            line="15"
            column="22"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.work:work-runtime than 2.9.0 is available: 2.10.2"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="work-runtime = &quot;2.9.0&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml"
            line="16"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.firebase:firebase-auth than 23.2.0 is available: 23.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="firebaseAuth = &quot;23.2.0&quot;"
        errorLine2="               ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\gradle\libs.versions.toml"
            line="19"
            column="16"/>
    </issue>

    <issue
        id="NotConstructor"
        severity="Warning"
        message="Method Devicer looks like a constructor but is a normal method"
        category="Correctness"
        priority="4"
        summary="Not a Constructor"
        explanation="This check catches methods that look like they were intended to be constructors, but aren&apos;t."
        errorLine1="    @FormUrlEncoded"
        errorLine2="    ^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\util\Devicer.java"
            line="15"
            column="5"/>
    </issue>

    <issue
        id="InnerclassSeparator"
        severity="Warning"
        message="Use &apos;$&apos; instead of &apos;.&apos; for inner classes; replace &quot;androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver&quot; with &quot;androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver&quot;"
        category="Correctness"
        priority="3"
        summary="Inner classes should use `$` rather than `.`"
        explanation="When you reference an inner class in a manifest file, you must use &apos;$&apos; instead of &apos;.&apos; as the separator character, i.e. Outer$Inner instead of Outer.Inner.&#xA;&#xA;(If you get this warning for a class which is not actually an inner class, it&apos;s because you are using uppercase characters in your package name, which is not conventional.)"
        errorLine1="            android:name=&quot;androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="109"
            column="27"/>
    </issue>

    <issue
        id="InnerclassSeparator"
        severity="Warning"
        message="Use &apos;$&apos; instead of &apos;.&apos; for inner classes; replace &quot;androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy&quot; with &quot;androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy&quot;"
        category="Correctness"
        priority="3"
        summary="Inner classes should use `$` rather than `.`"
        explanation="When you reference an inner class in a manifest file, you must use &apos;$&apos; instead of &apos;.&apos; as the separator character, i.e. Outer$Inner instead of Outer.Inner.&#xA;&#xA;(If you get this warning for a class which is not actually an inner class, it&apos;s because you are using uppercase characters in your package name, which is not conventional.)"
        errorLine1="            android:name=&quot;androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="114"
            column="27"/>
    </issue>

    <issue
        id="InnerclassSeparator"
        severity="Warning"
        message="Use &apos;$&apos; instead of &apos;.&apos; for inner classes; replace &quot;androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy&quot; with &quot;androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy&quot;"
        category="Correctness"
        priority="3"
        summary="Inner classes should use `$` rather than `.`"
        explanation="When you reference an inner class in a manifest file, you must use &apos;$&apos; instead of &apos;.&apos; as the separator character, i.e. Outer$Inner instead of Outer.Inner.&#xA;&#xA;(If you get this warning for a class which is not actually an inner class, it&apos;s because you are using uppercase characters in your package name, which is not conventional.)"
        errorLine1="            android:name=&quot;androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="124"
            column="27"/>
    </issue>

    <issue
        id="InnerclassSeparator"
        severity="Warning"
        message="Use &apos;$&apos; instead of &apos;.&apos; for inner classes; replace &quot;androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy&quot; with &quot;androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy&quot;"
        category="Correctness"
        priority="3"
        summary="Inner classes should use `$` rather than `.`"
        explanation="When you reference an inner class in a manifest file, you must use &apos;$&apos; instead of &apos;.&apos; as the separator character, i.e. Outer$Inner instead of Outer.Inner.&#xA;&#xA;(If you get this warning for a class which is not actually an inner class, it&apos;s because you are using uppercase characters in your package name, which is not conventional.)"
        errorLine1="            android:name=&quot;androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="134"
            column="27"/>
    </issue>

    <issue
        id="InnerclassSeparator"
        severity="Warning"
        message="Use &apos;$&apos; instead of &apos;.&apos; for inner classes; replace &quot;androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy&quot; with &quot;androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy&quot;"
        category="Correctness"
        priority="3"
        summary="Inner classes should use `$` rather than `.`"
        explanation="When you reference an inner class in a manifest file, you must use &apos;$&apos; instead of &apos;.&apos; as the separator character, i.e. Outer$Inner instead of Outer.Inner.&#xA;&#xA;(If you get this warning for a class which is not actually an inner class, it&apos;s because you are using uppercase characters in your package name, which is not conventional.)"
        errorLine1="            android:name=&quot;androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="144"
            column="27"/>
    </issue>

    <issue
        id="PxUsage"
        severity="Warning"
        message="Avoid using &quot;`px`&quot; as units; use &quot;`dp`&quot; instead"
        category="Correctness"
        priority="2"
        summary="Using &apos;px&apos; dimension"
        explanation="For performance reasons and to keep the code simpler, the Android system uses pixels as the standard unit for expressing dimension or coordinate values. That means that the dimensions of a view are always expressed in the code using pixels, but always based on the current screen density. For instance, if `myView.getWidth()` returns 10, the view is 10 pixels wide on the current screen, but on a device with a higher density screen, the value returned might be 15. If you use pixel values in your application code to work with bitmaps that are not pre-scaled for the current screen density, you might need to scale the pixel values that you use in your code to match the un-scaled bitmap source."
        url="https://developer.android.com/guide/practices/screens_support.html#screen-independence"
        urls="https://developer.android.com/guide/practices/screens_support.html#screen-independence"
        errorLine1="        android:layout_width=&quot;100px&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\custom_dialog.xml"
            line="7"
            column="9"/>
    </issue>

    <issue
        id="HardwareIds"
        severity="Warning"
        message="Using `getString` to get device identifiers is not recommended"
        category="Security"
        priority="6"
        summary="Hardware Id Usage"
        explanation="Using these device identifiers is not recommended other than for high value fraud prevention and advanced telephony use-cases. For advertising use-cases, use `AdvertisingIdClient$Info#getId` and for analytics, use `InstanceId#getId`."
        url="https://developer.android.com/training/articles/user-data-ids.html"
        urls="https://developer.android.com/training/articles/user-data-ids.html"
        errorLine1="                            &quot;&amp;device_id=&quot; + Settings.Secure.getString(MainActivity.this.getContentResolver(), &quot;android_id&quot;);"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="1371"
            column="45"/>
    </issue>

    <issue
        id="UnprotectedSMSBroadcastReceiver"
        severity="Warning"
        message="BroadcastReceivers that declare an intent-filter for `SMS_DELIVER` or `SMS_RECEIVED` must ensure that the caller has the `BROADCAST_SMS` permission, otherwise it is possible for malicious actors to spoof intents"
        category="Security"
        priority="6"
        summary="Unprotected SMS `BroadcastReceiver`"
        explanation="BroadcastReceivers that declare an intent-filter for `SMS_DELIVER` or `SMS_RECEIVED` must ensure that the caller has the `BROADCAST_SMS` permission, otherwise it is possible for malicious actors to spoof intents."
        url="https://goo.gle/UnprotectedSMSBroadcastReceiver"
        urls="https://goo.gle/UnprotectedSMSBroadcastReceiver"
        errorLine1="        &lt;receiver android:name=&quot;com.appystore.mrecharge.IncomingSms&quot;"
        errorLine2="         ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="50"
            column="10"/>
    </issue>

    <issue
        id="ExportedService"
        severity="Warning"
        message="Exported service does not require permission"
        category="Security"
        priority="5"
        summary="Exported service does not require permission"
        explanation="Exported services (services which either set `exported=true` or contain an intent-filter and do not specify `exported=false`) should define a permission that an entity must have in order to launch the service or bind to it. Without this, any application can use this service."
        url="https://goo.gle/ExportedService"
        urls="https://goo.gle/ExportedService"
        errorLine1="        &lt;service"
        errorLine2="         ~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml"
            line="63"
            column="10"/>
    </issue>

    <issue
        id="InsecureBaseConfiguration"
        severity="Warning"
        message="Insecure Base Configuration"
        category="Security"
        priority="5"
        summary="Insecure Base Configuration"
        explanation="Permitting cleartext traffic could allow eavesdroppers to intercept data sent by your app, which impacts the privacy of your users. Consider only allowing encrypted traffic by setting the `cleartextTrafficPermitted` tag to `false`."
        url="https://goo.gle/InsecureBaseConfiguration"
        urls="https://goo.gle/InsecureBaseConfiguration,https://developer.android.com/preview/features/security-config.html"
        errorLine1="    &lt;base-config cleartextTrafficPermitted=&quot;true&quot;>"
        errorLine2="                                            ~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\xml\network_security_config.xml"
            line="3"
            column="45"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        severity="Warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        category="Performance"
        priority="8"
        summary="Invalidating All RecyclerView Data"
        explanation="The `RecyclerView` adapter&apos;s `onNotifyDataSetChanged` method does not specify what about the data set has changed, forcing any observers to assume that all existing items and structure may no longer be valid. `LayoutManager`s will be forced to fully rebind and relayout all visible views."
        errorLine1="                    this.recyclerviewItemAdapter.notifyDataSetChanged();"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Monitoring.java"
            line="137"
            column="21"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        severity="Warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        category="Performance"
        priority="8"
        summary="Invalidating All RecyclerView Data"
        explanation="The `RecyclerView` adapter&apos;s `onNotifyDataSetChanged` method does not specify what about the data set has changed, forcing any observers to assume that all existing items and structure may no longer be valid. `LayoutManager`s will be forced to fully rebind and relayout all visible views."
        errorLine1="                    this.recyclerviewItemAdapter.notifyDataSetChanged();"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Monitoring.java"
            line="188"
            column="21"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        severity="Warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        category="Performance"
        priority="8"
        summary="Invalidating All RecyclerView Data"
        explanation="The `RecyclerView` adapter&apos;s `onNotifyDataSetChanged` method does not specify what about the data set has changed, forcing any observers to assume that all existing items and structure may no longer be valid. `LayoutManager`s will be forced to fully rebind and relayout all visible views."
        errorLine1="                this.recyclerviewItemAdapter.notifyDataSetChanged();"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Monitoring.java"
            line="199"
            column="17"/>
    </issue>

    <issue
        id="Recycle"
        severity="Warning"
        message="This `Cursor` should be freed up after use with `#close()`"
        category="Performance"
        priority="7"
        summary="Missing `recycle()` calls"
        explanation="Many resources, such as TypedArrays, VelocityTrackers, etc., should be recycled (with a `recycle()` call) after use. This lint check looks for missing `recycle()` calls."
        errorLine1="        return readableDatabase.rawQuery(sb.toString(), null).getCount() == 0;"
        errorLine2="                                ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="71"
            column="33"/>
    </issue>

    <issue
        id="Recycle"
        severity="Warning"
        message="This `Cursor` should be freed up after use with `#close()`"
        category="Performance"
        priority="7"
        summary="Missing `recycle()` calls"
        explanation="Many resources, such as TypedArrays, VelocityTrackers, etc., should be recycled (with a `recycle()` call) after use. This lint check looks for missing `recycle()` calls."
        errorLine1="        return readableDatabase.rawQuery(sb.toString(), null).getCount() == 0;"
        errorLine2="                                ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="189"
            column="33"/>
    </issue>

    <issue
        id="Recycle"
        severity="Warning"
        message="This `Cursor` should be freed up after use with `#close()`"
        category="Performance"
        priority="7"
        summary="Missing `recycle()` calls"
        explanation="Many resources, such as TypedArrays, VelocityTrackers, etc., should be recycled (with a `recycle()` call) after use. This lint check looks for missing `recycle()` calls."
        errorLine1="        return readableDatabase.rawQuery(sb.toString(), null).getCount() == 0;"
        errorLine2="                                ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="200"
            column="33"/>
    </issue>

    <issue
        id="Recycle"
        severity="Warning"
        message="This `Cursor` should be freed up after use with `#close()`"
        category="Performance"
        priority="7"
        summary="Missing `recycle()` calls"
        explanation="Many resources, such as TypedArrays, VelocityTrackers, etc., should be recycled (with a `recycle()` call) after use. This lint check looks for missing `recycle()` calls."
        errorLine1="            if (writableDatabase.rawQuery(&quot;select * from income_log where status!=0&quot;, null).getCount() > 40) {"
        errorLine2="                                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\DbHelper.java"
            line="285"
            column="34"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_alignParentLeft`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="                android:layout_alignParentLeft=&quot;true&quot;>"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="97"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_alignParentRight`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="                    android:layout_alignParentRight=&quot;true&quot;/>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="208"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_below`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="                android:layout_below=&quot;@+id/blb&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="287"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (telecomManager != null &amp;&amp; Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\Dialfunction.java"
            line="140"
            column="39"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= 23) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="305"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                if (Build.VERSION.SDK_INT >= 23) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="388"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is never &lt; 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (i != ACTION_MANAGE_OVERLAY_PERMISSION_REQUEST_CODE || Build.VERSION.SDK_INT &lt; 23 || android.provider.Settings.canDrawOverlays(getApplicationContext())) {"
        errorLine2="                                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="561"
            column="67"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= 23) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="600"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= 23) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="1297"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is never &lt; 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT &lt;= Build.VERSION_CODES.KITKAT_WATCH) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\util\NotificationUtils.java"
            line="146"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\PushService.java"
            line="142"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\Smsend.java"
            line="194"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="This folder configuration (`v24`) is unnecessary; `minSdkVersion` is 24. Merge all the resources in this folder into `drawable`."
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder.">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable-v24"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        severity="Warning"
        message="This `AsyncTask` class should be static or leaks might occur (com.appystore.mrecharge.activity.MainActivity.Checkstatus)"
        category="Performance"
        priority="6"
        summary="Static Field Leaks"
        explanation="A static field will leak contexts.&#xA;&#xA;Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a `Fragment` or `Activity`, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.&#xA;&#xA;Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.&#xA;&#xA;ViewModel classes should never point to Views or non-application Contexts."
        errorLine1="    private class Checkstatus extends AsyncTask&lt;Void, Void, Boolean> {"
        errorLine2="                  ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="833"
            column="19"/>
    </issue>

    <issue
        id="VectorPath"
        severity="Warning"
        message="Very long vector path (1089 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        category="Performance"
        priority="5"
        summary="Long vector paths"
        explanation="Using long vector paths is bad for performance. There are several ways to make the `pathData` shorter:&#xA;* Using less precision&#xA;* Removing some minor details&#xA;* Using the Android Studio vector conversion tool&#xA;* Rasterizing the image (converting to PNG)"
        errorLine1="      &lt;path android:pathData=&quot;M395.54688,-27.21875Q395.54688,-32.625,391.70312,-35.640625Q387.875,-38.671875,378.23438,-41.90625Q368.60938,-45.140625,362.5,-48.171875Q342.59375,-57.9375,342.59375,-75.03125Q342.59375,-83.53125,347.54688,-90.03125Q352.51562,-96.546875,361.57812,-100.15625Q370.65625,-103.78125,381.96875,-103.78125Q393.01562,-103.78125,401.76562,-99.84375Q410.51562,-95.90625,415.35938,-88.625Q420.21875,-81.359375,420.21875,-72L395.60938,-72Q395.60938,-78.265625,391.78125,-81.703125Q387.95312,-85.15625,381.40625,-85.15625Q374.79688,-85.15625,370.96875,-82.234375Q367.14062,-79.3125,367.14062,-74.8125Q367.14062,-70.875,371.35938,-67.671875Q375.57812,-64.484375,386.1875,-61.0625Q396.8125,-57.65625,403.625,-53.71875Q420.21875,-44.15625,420.21875,-27.359375Q420.21875,-13.921875,410.09375,-6.25Q399.96875,1.40625,382.32812,1.40625Q369.875,1.40625,359.78125,-3.0625Q349.70312,-7.53125,344.59375,-15.296875Q339.5,-23.0625,339.5,-33.1875L364.25,-33.1875Q364.25,-24.96875,368.5,-21.0625Q372.76562,-17.15625,382.32812,-17.15625Q388.4375,-17.15625,391.98438,-19.796875Q395.54688,-22.4375,395.54688,-27.21875Z&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_launcher_foreground.xml"
            line="20"
            column="31"/>
    </issue>

    <issue
        id="VectorPath"
        severity="Warning"
        message="Very long vector path (882 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        category="Performance"
        priority="5"
        summary="Long vector paths"
        explanation="Using long vector paths is bad for performance. There are several ways to make the `pathData` shorter:&#xA;* Using less precision&#xA;* Removing some minor details&#xA;* Using the Android Studio vector conversion tool&#xA;* Rasterizing the image (converting to PNG)"
        errorLine1="      &lt;path android:pathData=&quot;M1191.5,0Q1190.2344,-2.328125,1189.25,-6.828125Q1182.7188,1.40625,1170.9688,1.40625Q1160.2188,1.40625,1152.6875,-5.09375Q1145.1719,-11.609375,1145.1719,-21.453125Q1145.1719,-33.828125,1154.3125,-40.15625Q1163.4531,-46.484375,1180.8906,-46.484375L1188.2031,-46.484375L1188.2031,-50.484375Q1188.2031,-60.96875,1179.125,-60.96875Q1170.6875,-60.96875,1170.6875,-52.671875L1147,-52.671875Q1147,-63.703125,1156.375,-70.59375Q1165.7656,-77.484375,1180.3125,-77.484375Q1194.875,-77.484375,1203.3125,-70.375Q1211.75,-63.28125,1211.9688,-50.90625L1211.9688,-17.234375Q1212.1094,-6.75,1215.2031,-1.203125L1215.2031,0L1191.5,0ZM1176.6719,-15.46875Q1181.0938,-15.46875,1184.0156,-17.359375Q1186.9375,-19.265625,1188.2031,-21.65625L1188.2031,-33.828125L1181.3125,-33.828125Q1168.9375,-33.828125,1168.9375,-22.71875Q1168.9375,-19.484375,1171.1094,-17.46875Q1173.2969,-15.46875,1176.6719,-15.46875Z&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_launcher_foreground.xml"
            line="40"
            column="31"/>
    </issue>

    <issue
        id="VectorPath"
        severity="Warning"
        message="Very long vector path (943 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        category="Performance"
        priority="5"
        summary="Long vector paths"
        explanation="Using long vector paths is bad for performance. There are several ways to make the `pathData` shorter:&#xA;* Using less precision&#xA;* Removing some minor details&#xA;* Using the Android Studio vector conversion tool&#xA;* Rasterizing the image (converting to PNG)"
        errorLine1="      &lt;path android:pathData=&quot;M1276.875,-38.609375Q1276.875,-50.140625,1280.6719,-59Q1284.4688,-67.859375,1291.5625,-72.671875Q1298.6719,-77.484375,1308.0938,-77.484375Q1319.9688,-77.484375,1326.6562,-69.46875L1327.5,-76.078125L1349.0156,-76.078125L1349.0156,-2.890625Q1349.0156,7.171875,1344.3281,14.59375Q1339.6562,22.015625,1330.7969,25.984375Q1321.9375,29.953125,1310.2656,29.953125Q1301.9062,29.953125,1294.0625,26.78125Q1286.2188,23.625,1282.0781,18.5625L1291.9844,4.640625Q1298.5938,12.453125,1309.4219,12.453125Q1325.1719,12.453125,1325.1719,-3.734375L1325.1719,-6.125Q1318.3594,1.40625,1307.9531,1.40625Q1294.0312,1.40625,1285.4531,-9.25Q1276.875,-19.90625,1276.875,-37.765625L1276.875,-38.609375ZM1300.6406,-37.125Q1300.6406,-27.78125,1304.1562,-22.328125Q1307.6719,-16.875,1314,-16.875Q1321.6562,-16.875,1325.1719,-22.015625L1325.1719,-54Q1321.7344,-59.203125,1314.1406,-59.203125Q1307.8125,-59.203125,1304.2188,-53.546875Q1300.6406,-47.890625,1300.6406,-37.125Z&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_launcher_foreground.xml"
            line="44"
            column="31"/>
    </issue>

    <issue
        id="UseValueOf"
        severity="Warning"
        message="Use `Integer.valueOf(matcher.group().toString())` instead"
        category="Performance"
        priority="4"
        summary="Should use `valueOf` instead of `new`"
        explanation="You should not call the constructor for wrapper classes directly, such as`new Integer(42)`. Instead, call the `valueOf` factory method, such as `Integer.valueOf(42)`. This will typically use less memory because common integers such as 0 and 1 will share a single instance."
        errorLine1="                    int intValue = new Integer(matcher.group().toString()).intValue();"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java"
            line="91"
            column="36"/>
    </issue>

    <issue
        id="UseValueOf"
        severity="Warning"
        message="Use `Integer.valueOf(matcher.group().toString())` instead"
        category="Performance"
        priority="4"
        summary="Should use `valueOf` instead of `new`"
        explanation="You should not call the constructor for wrapper classes directly, such as`new Integer(42)`. Instead, call the `valueOf` factory method, such as `Integer.valueOf(42)`. This will typically use less memory because common integers such as 0 and 1 will share a single instance."
        errorLine1="                    int intValue = new Integer(matcher.group().toString()).intValue();"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java"
            line="216"
            column="36"/>
    </issue>

    <issue
        id="UseValueOf"
        severity="Warning"
        message="Use `Integer.valueOf(matcher.group().toString())` instead"
        category="Performance"
        priority="4"
        summary="Should use `valueOf` instead of `new`"
        explanation="You should not call the constructor for wrapper classes directly, such as`new Integer(42)`. Instead, call the `valueOf` factory method, such as `Integer.valueOf(42)`. This will typically use less memory because common integers such as 0 and 1 will share a single instance."
        errorLine1="                    int intValue = new Integer(matcher.group().toString()).intValue();"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\Tes.java"
            line="240"
            column="36"/>
    </issue>

    <issue
        id="UseValueOf"
        severity="Warning"
        message="Use `Integer.valueOf(matcher.group().toString())` instead"
        category="Performance"
        priority="4"
        summary="Should use `valueOf` instead of `new`"
        explanation="You should not call the constructor for wrapper classes directly, such as`new Integer(42)`. Instead, call the `valueOf` factory method, such as `Integer.valueOf(42)`. This will typically use less memory because common integers such as 0 and 1 will share a single instance."
        errorLine1="                    int intValue = new Integer(matcher.group().toString()).intValue();"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="479"
            column="36"/>
    </issue>

    <issue
        id="UseValueOf"
        severity="Warning"
        message="Use `Integer.valueOf(matcher.group().toString())` instead"
        category="Performance"
        priority="4"
        summary="Should use `valueOf` instead of `new`"
        explanation="You should not call the constructor for wrapper classes directly, such as`new Integer(42)`. Instead, call the `valueOf` factory method, such as `Integer.valueOf(42)`. This will typically use less memory because common integers such as 0 and 1 will share a single instance."
        errorLine1="                    int intValue = new Integer(matcher.group().toString()).intValue();"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\service\USSDService.java"
            line="502"
            column="36"/>
    </issue>

    <issue
        id="DisableBaselineAlignment"
        severity="Warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance"
        category="Performance"
        priority="3"
        summary="Missing `baselineAligned` attribute"
        explanation="When a `LinearLayout` is used to distribute the space proportionally between nested layouts, the baseline alignment property should be turned off to make the layout computation faster."
        errorLine1="    &lt;LinearLayout"
        errorLine2="     ~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="8"
            column="6"/>
    </issue>

    <issue
        id="DisableBaselineAlignment"
        severity="Warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance"
        category="Performance"
        priority="3"
        summary="Missing `baselineAligned` attribute"
        explanation="When a `LinearLayout` is used to distribute the space proportionally between nested layouts, the baseline alignment property should be turned off to make the layout computation faster."
        errorLine1="    &lt;LinearLayout"
        errorLine2="     ~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="10"
            column="6"/>
    </issue>

    <issue
        id="DisableBaselineAlignment"
        severity="Warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance"
        category="Performance"
        priority="3"
        summary="Missing `baselineAligned` attribute"
        explanation="When a `LinearLayout` is used to distribute the space proportionally between nested layouts, the baseline alignment property should be turned off to make the layout computation faster."
        errorLine1="    &lt;LinearLayout"
        errorLine2="     ~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="8"
            column="6"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `#ff1b1a1a` with a theme that also paints a background (inferred theme is `@style/Theme.AppyStoreMRecharge`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;#ff1b1a1a&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\item_todo.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `#ff1b1a1a` with a theme that also paints a background (inferred theme is `@style/Theme.AppyStoreMRecharge`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;#ff1b1a1a&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\listmain.xml"
            line="3"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.appystoremrecharge` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\appystoremrecharge.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.bg` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\bg.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.menu.bottom_navigation_menu` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;menu xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\bottom_navigation_menu.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.button_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\button_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.card_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\card_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.config` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot; xmlns:app=&quot;http://schemas.android.com/apk/res-auto&quot;"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.custom_dialog` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;RelativeLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\custom_dialog.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_launcher_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_launcher_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.ic_launcher_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;ic_launcher_background&quot;>#00C1EC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\values\ic_launcher_background.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_launcher_foreground` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_launcher_foreground.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.input_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\input_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.modern_toggle_off` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\modern_toggle_off.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.modern_toggle_on` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\modern_toggle_on.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.modern_toggle_selector` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\modern_toggle_selector.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.nav_item_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\nav_item_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.raw.notification` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\raw\notification.mp3"/>
    </issue>

    <issue
        id="UselessParent"
        severity="Warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary; transfer the `background` attribute to the other view"
        category="Performance"
        priority="2"
        summary="Unnecessary parent layout"
        explanation="A layout with children that has no siblings, is not a scrollview or a root layout, and does not have a background, can be removed and have its children moved directly into the parent for a flatter and more efficient layout hierarchy."
        errorLine1="            &lt;LinearLayout"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="89"
            column="14"/>
    </issue>

    <issue
        id="IconLauncherShape"
        severity="Warning"
        message="Launcher icon used as round icon did not have a circular shape"
        category="Usability:Icons"
        priority="6"
        summary="The launcher icon shape should use a distinct silhouette"
        explanation="According to the Android Design Guide (https://d.android.com/r/studio-ui/designer/material/iconography) your launcher icons should &quot;use a distinct silhouette&quot;, a &quot;three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth.&quot;&#xA;&#xA;The unique silhouette implies that your launcher icon should not be a filled square.">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-hdpi\ic_launcher.png"/>
    </issue>

    <issue
        id="IconLauncherShape"
        severity="Warning"
        message="Launcher icon used as round icon did not have a circular shape"
        category="Usability:Icons"
        priority="6"
        summary="The launcher icon shape should use a distinct silhouette"
        explanation="According to the Android Design Guide (https://d.android.com/r/studio-ui/designer/material/iconography) your launcher icons should &quot;use a distinct silhouette&quot;, a &quot;three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth.&quot;&#xA;&#xA;The unique silhouette implies that your launcher icon should not be a filled square.">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-mdpi\ic_launcher.png"/>
    </issue>

    <issue
        id="IconLauncherShape"
        severity="Warning"
        message="Launcher icon used as round icon did not have a circular shape"
        category="Usability:Icons"
        priority="6"
        summary="The launcher icon shape should use a distinct silhouette"
        explanation="According to the Android Design Guide (https://d.android.com/r/studio-ui/designer/material/iconography) your launcher icons should &quot;use a distinct silhouette&quot;, a &quot;three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth.&quot;&#xA;&#xA;The unique silhouette implies that your launcher icon should not be a filled square.">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-xhdpi\ic_launcher.png"/>
    </issue>

    <issue
        id="IconLauncherShape"
        severity="Warning"
        message="Launcher icon used as round icon did not have a circular shape"
        category="Usability:Icons"
        priority="6"
        summary="The launcher icon shape should use a distinct silhouette"
        explanation="According to the Android Design Guide (https://d.android.com/r/studio-ui/designer/material/iconography) your launcher icons should &quot;use a distinct silhouette&quot;, a &quot;three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth.&quot;&#xA;&#xA;The unique silhouette implies that your launcher icon should not be a filled square.">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-xxhdpi\ic_launcher.png"/>
    </issue>

    <issue
        id="IconLauncherShape"
        severity="Warning"
        message="Launcher icon used as round icon did not have a circular shape"
        category="Usability:Icons"
        priority="6"
        summary="The launcher icon shape should use a distinct silhouette"
        explanation="According to the Android Design Guide (https://d.android.com/r/studio-ui/designer/material/iconography) your launcher icons should &quot;use a distinct silhouette&quot;, a &quot;three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth.&quot;&#xA;&#xA;The unique silhouette implies that your launcher icon should not be a filled square.">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/appystoremrecharge.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\appystoremrecharge.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/bg.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\bg.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/ic_stat_name.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_stat_name.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/toggle_off.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\toggle_off.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/toggle_on.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\toggle_on.png"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="228"
            column="18"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="147"
            column="18"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="184"
            column="18"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="221"
            column="18"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="242"
            column="14"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="262"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="228"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="248"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="107"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="127"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="147"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="167"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="187"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="147"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="184"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="221"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="242"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="262"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="81"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="123"
            column="22"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    coreLibraryDesugaring &apos;com.android.tools:desugar_jdk_libs:2.1.3&apos;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle"
            line="34"
            column="27"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead (com.squareup.okhttp3:okhttp is already available as `okhttp`, but using version 4.12.0 instead)"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.squareup.okhttp3:okhttp:4.11.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle"
            line="48"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead (com.squareup.okhttp3:logging-interceptor is already available as `logging-interceptor`, but using version 4.12.0 instead)"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.squareup.okhttp3:logging-interceptor:4.11.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle"
            line="49"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.annotation:annotation:1.7.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle"
            line="52"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    debugImplementation &apos;com.facebook.stetho:stetho:1.6.0&apos;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle"
            line="71"
            column="25"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    debugImplementation &apos;com.facebook.stetho:stetho-okhttp3:1.6.0&apos;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build.gradle"
            line="72"
            column="25"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="42"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="75"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="107"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="139"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="                &lt;ImageView"
        errorLine2="                 ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="176"
            column="18"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageButton"
        errorLine2="             ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="22"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageButton"
        errorLine2="             ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="43"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageButton"
        errorLine2="             ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="64"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageButton"
        errorLine2="             ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="28"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageButton"
        errorLine2="             ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="54"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageButton"
        errorLine2="             ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="79"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageButton"
        errorLine2="             ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="23"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageButton"
        errorLine2="             ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="45"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageButton"
        errorLine2="             ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="67"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="93"
            column="14"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        this.myidt.setText(&quot;Device ID: &quot; + getPrefid(&quot;myid&quot;, getApplicationContext()));"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="271"
            column="28"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        this.myidt.setText(&quot;Device ID: &quot; + getPrefid(&quot;myid&quot;, getApplicationContext()));"
        errorLine2="                           ~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\activity\MainActivity.java"
            line="271"
            column="28"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    holder.name.setText(&quot;Waiting...&quot;);"
        errorLine2="                                        ~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\RecyclerviewItemAdapter.java"
            line="47"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    holder.name.setText(&quot;Done&quot;);"
        errorLine2="                                        ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\RecyclerviewItemAdapter.java"
            line="51"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    holder.name.setText(&quot;Processing&quot;);"
        errorLine2="                                        ~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\RecyclerviewItemAdapter.java"
            line="55"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    holder.name.setText(&quot;Failed&quot;);"
        errorLine2="                                        ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\RecyclerviewItemAdapter.java"
            line="59"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                holder.price.setText(&quot;No details available&quot;);"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\java\com\appystore\mrecharge\RecyclerviewItemAdapter.java"
            line="67"
            column="38"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Home&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="52"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Banking&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Banking&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="85"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Settings&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="117"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Monitor&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Monitor&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="149"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Automatic Mobile Recharge System&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Automatic Mobile Recharge System&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="189"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Device ID: **********&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Device ID: **********&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="203"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;License Information&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;License Information&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="223"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;License Key eg: xxxxx-xxxxx-xxxxx-xxxxx&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;License Key eg: xxxxx-xxxxx-xxxxx-xxxxx&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="236"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;PIN&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;PIN&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="256"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;1234&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;1234&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="259"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;V.1&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;V.1&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="276"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;V.2&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;V.2&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="284"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Network Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Network Settings&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="315"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;SIM A:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;SIM A:&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="333"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;SIM B:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;SIM B:&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="359"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;System Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;System Settings&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="388"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;SMS Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;SMS Service&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="407"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Accessibility&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Accessibility&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="436"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Power Optimize&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Power Optimize&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="465"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Find Page&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Find Page&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="493"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:title=&quot;Home&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\bottom_navigation_menu.xml"
            line="6"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;M Banking&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:title=&quot;M Banking&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\bottom_navigation_menu.xml"
            line="10"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:title=&quot;Settings&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\bottom_navigation_menu.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Monitor&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:title=&quot;Monitor&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\bottom_navigation_menu.xml"
            line="18"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Home&quot;/>"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="34"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;M Banking&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;M Banking&quot;/>"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="55"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Settings&quot;/>"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="76"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;GP&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;GP&quot;/>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="106"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter USSD&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;Enter USSD&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="116"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;ROBI&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;ROBI&quot;/>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="126"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter USSD&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;Enter USSD&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="136"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;AIRTEL&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;AIRTEL&quot;/>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="146"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter USSD&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;Enter USSD&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="156"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;BANGLALINK&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;BANGLALINK&quot;/>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="166"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter USSD&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;Enter USSD&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="176"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;TELETALK&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;TELETALK&quot;/>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="186"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter USSD&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;Enter USSD&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="196"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Save&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Save&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="207"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot; Ok &quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot; Ok &quot;/>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\custom_dialog.xml"
            line="11"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Home&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="40"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;M Banking&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;M Banking&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="66"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Settings&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="91"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Forward Rquest to sms&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Forward Rquest to sms&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="104"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Bkash&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Bkash&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="127"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter number here&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;Enter number here&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="153"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Rocket&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Rocket&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="164"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter number here&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;Enter number here&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="190"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Nogad&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Nogad&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="201"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter number here&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;Enter number here&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="227"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Request Every sec&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Request Every sec&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="238"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;0 sec&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:hint=&quot;0 sec&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="248"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Maximum Try&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Maximum Try&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="258"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;0&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:hint=&quot;0&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="268"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Dial function&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Dial function&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="279"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Save&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Save&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="299"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;License Activation&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;License Activation&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="38"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter your license key and PIN to activate&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Enter your license key and PIN to activate&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="48"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;License Key&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;License Key&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="74"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter your license key&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;Enter your license key&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="85"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;PIN&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;PIN&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="116"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter your PIN&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;Enter your PIN&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="127"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Activate License&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Activate License&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="145"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Please enter your license key and PIN to activate&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Please enter your license key and PIN to activate&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="160"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🌐&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;🌐&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="202"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Domain Access&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;Domain Access&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="209"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Your domain: Loading...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;Your domain: Loading...&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="221"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🎛️ Open Dashboard&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;🎛️ Open Dashboard&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="231"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;💡 View your license details and domain information&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;💡 View your license details and domain information&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="244"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📅&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;📅&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="288"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;License Status&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;License Status&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="295"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;License expires: Loading...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;License expires: Loading...&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="307"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Time remaining: Calculating...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;Time remaining: Calculating...&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="317"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;💡 You will receive warnings 2 days before expiration&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;💡 You will receive warnings 2 days before expiration&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="337"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;👨‍💻&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;👨‍💻&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="374"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Md Sadrul Hasan Dider&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Md Sadrul Hasan Dider&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="381"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;© 2025 License Activation System&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;© 2025 License Activation System&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml"
            line="392"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Mobile Banking&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:title=&quot;Mobile Banking&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\menu_main.xml"
            line="6"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Forward&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:title=&quot;Forward&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\menu_main.xml"
            line="11"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Home&quot;/>"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="35"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;M Banking&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;M Banking&quot;/>"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="57"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Settings&quot;/>"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="79"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Automatic Mobile recharge system&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Automatic Mobile recharge system&quot;/>"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="107"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Bkash&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Bkash&quot;/>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="122"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Rocket&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Rocket&quot;/>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="149"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Nogad&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Nogad&quot;/>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="176"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Upay&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Upay&quot;/>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="203"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Bill pay&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Bill pay&quot;/>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="230"
            column="21"/>
    </issue>

    <issue
        id="RtlSymmetry"
        severity="Warning"
        message="When you define `paddingRight` you should probably also define `paddingLeft` for right-to-left symmetry"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Padding and margin symmetry"
        explanation="If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry."
        errorLine1="                            android:paddingRight=&quot;15dp&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="275"
            column="29"/>
    </issue>

    <issue
        id="RtlSymmetry"
        severity="Warning"
        message="When you define `paddingRight` you should probably also define `paddingLeft` for right-to-left symmetry"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Padding and margin symmetry"
        explanation="If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry."
        errorLine1="                            android:paddingRight=&quot;15dp&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="283"
            column="29"/>
    </issue>

    <issue
        id="RtlSymmetry"
        severity="Warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Padding and margin symmetry"
        explanation="If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry."
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="31"
            column="17"/>
    </issue>

    <issue
        id="RtlSymmetry"
        severity="Warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Padding and margin symmetry"
        explanation="If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry."
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="52"
            column="17"/>
    </issue>

    <issue
        id="RtlSymmetry"
        severity="Warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Padding and margin symmetry"
        explanation="If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry."
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="73"
            column="17"/>
    </issue>

    <issue
        id="RtlSymmetry"
        severity="Warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Padding and margin symmetry"
        explanation="If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry."
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="39"
            column="17"/>
    </issue>

    <issue
        id="RtlSymmetry"
        severity="Warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Padding and margin symmetry"
        explanation="If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry."
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="65"
            column="17"/>
    </issue>

    <issue
        id="RtlSymmetry"
        severity="Warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Padding and margin symmetry"
        explanation="If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry."
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="90"
            column="17"/>
    </issue>

    <issue
        id="RtlSymmetry"
        severity="Warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Padding and margin symmetry"
        explanation="If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry."
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="32"
            column="17"/>
    </issue>

    <issue
        id="RtlSymmetry"
        severity="Warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Padding and margin symmetry"
        explanation="If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry."
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="54"
            column="17"/>
    </issue>

    <issue
        id="RtlSymmetry"
        severity="Warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Padding and margin symmetry"
        explanation="If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry."
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="76"
            column="17"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingRight` with `android:paddingEnd=&quot;15dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                            android:paddingRight=&quot;15dp&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="275"
            column="29"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingRight` with `android:paddingEnd=&quot;15dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                            android:paddingRight=&quot;15dp&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml"
            line="283"
            column="29"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="31"
            column="17"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="52"
            column="17"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="73"
            column="17"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_alignParentLeft` with `android:layout_alignParentStart=&quot;true&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                android:layout_alignParentLeft=&quot;true&quot;>"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="97"
            column="17"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`end`&quot; instead of &quot;`right`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                    android:layout_gravity=&quot;right&quot;"
        errorLine2="                                            ~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="200"
            column="45"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;20dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                    android:layout_marginRight=&quot;20dp&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="206"
            column="21"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_alignParentRight` with `android:layout_alignParentEnd=&quot;true&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                    android:layout_alignParentRight=&quot;true&quot;/>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml"
            line="208"
            column="21"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_marginRight=&quot;5dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\custom_dialog.xml"
            line="10"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="39"
            column="17"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="65"
            column="17"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml"
            line="90"
            column="17"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="32"
            column="17"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="54"
            column="17"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                android:paddingLeft=&quot;5dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml"
            line="76"
            column="17"/>
    </issue>

</issues>
