package com.appystore.mrecharge.service;

import android.app.Activity;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.telephony.SmsManager;
import android.util.Log;
import android.widget.Toast;

import androidx.core.app.NotificationCompat;

import com.appystore.mrecharge.DbHelper;
import com.appystore.mrecharge.R;
import com.appystore.mrecharge.activity.MainActivity;

import java.util.ArrayList;

public class Smsend extends Service {
    private static final String TAG = "SmsendService";
    private static final String CHANNEL_ID = "SmsServiceChannel";
    private static final int NOTIFICATION_ID = 5;

    // Constants for SMS status
    private static final int RESULT_OK = Activity.RESULT_OK;
    private static final int RESULT_CANCELED = Activity.RESULT_CANCELED;

    private final BroadcastReceiver smsSentReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String message;
            switch (getResultCode()) {
                case RESULT_OK:
                    message = "SMS sent successfully";
                    break;
                case SmsManager.RESULT_ERROR_GENERIC_FAILURE:
                    message = "Generic failure";
                    break;
                case SmsManager.RESULT_ERROR_RADIO_OFF:
                    message = "Radio off";
                    break;
                case SmsManager.RESULT_ERROR_NULL_PDU:
                    message = "Null PDU";
                    break;
                case SmsManager.RESULT_ERROR_NO_SERVICE:
                    message = "No service";
                    break;
                default:
                    message = "Unknown error code " + getResultCode();
                    break;
            }
            Log.d(TAG, "SMS sent status: " + message);
            Toast.makeText(getBaseContext(), message, Toast.LENGTH_SHORT).show();

            try {
                unregisterReceiver(this);
            } catch (IllegalArgumentException e) {
                Log.w(TAG, "Error unregistering smsSentReceiver", e);
            }
        }
    };

    private final BroadcastReceiver smsDeliveredReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String message;
            switch (getResultCode()) {
                case RESULT_OK:
                    message = "SMS delivered";
                    break;
                case RESULT_CANCELED:
                    message = "SMS not delivered";
                    break;
                default:
                    message = "Unknown delivery status: " + getResultCode();
                    break;
            }
            Log.d(TAG, "SMS delivery status: " + message);
            Toast.makeText(getBaseContext(), message, Toast.LENGTH_SHORT).show();

            try {
                unregisterReceiver(this);
            } catch (IllegalArgumentException e) {
                Log.w(TAG, "Error unregistering smsDeliveredReceiver", e);
            }
        }
    };

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Service onCreate");
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "Service onDestroy");
        super.onDestroy();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "Service onStartCommand");

        if (intent == null || intent.getExtras() == null) {
            Log.e(TAG, "Intent or extras is null");
            stopSelf();
            return START_NOT_STICKY;
        }

        String phoneNumber = intent.getExtras().getString(DbHelper.CONTACTS_COLUMN_NUMBER);
        String messageText = intent.getExtras().getString("text");

        if (phoneNumber == null || messageText == null) {
            Log.e(TAG, "Phone number or message text is null");
            stopSelf();
            return START_NOT_STICKY;
        }

        // Start as foreground service
        startForegroundService("Sending SMS to " + phoneNumber);

        // Send the SMS
        sendSMS(phoneNumber, messageText);

        // Stop the service after sending
        new Handler().postDelayed(() -> {
            stopForeground(true);
            stopSelf();
        }, 5000); // Give 5 seconds for SMS to be sent

        return START_NOT_STICKY;
    }

    private void startForegroundService(String contentText) {
        createNotificationChannel();

        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("SMS Service")
                .setContentText(contentText)
                .setSmallIcon(R.drawable.ic_stat_name)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT);

        startForeground(NOTIFICATION_ID, builder.build());
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "SMS Service Channel",
                    NotificationManager.IMPORTANCE_DEFAULT
            );
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }

    public void sendSMS(String phoneNumber, String message) {
        if (phoneNumber == null || message == null || phoneNumber.isEmpty() || message.isEmpty()) {
            Log.e(TAG, "Cannot send SMS, phone number or message is empty");
            return;
        }

        Log.d(TAG, "Sending SMS to " + phoneNumber);

        try {
            SmsManager smsManager = SmsManager.getDefault();
            ArrayList<String> parts = smsManager.divideMessage(message);

            ArrayList<PendingIntent> sentIntents = new ArrayList<>();
            ArrayList<PendingIntent> deliveredIntents = new ArrayList<>();

            int flags = 0;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags = PendingIntent.FLAG_IMMUTABLE;
            }

            PendingIntent sentPI = PendingIntent.getBroadcast(this, 0,
                    new Intent("SMS_SENT"), flags);
            PendingIntent deliveredPI = PendingIntent.getBroadcast(this, 0,
                    new Intent("SMS_DELIVERED"), flags);

            // Register for SMS sent status
            registerReceiver(smsSentReceiver, new IntentFilter("SMS_SENT"));

            // Register for SMS delivery status
            registerReceiver(smsDeliveredReceiver, new IntentFilter("SMS_DELIVERED"));

            // Add intents for each part
            for (int i = 0; i < parts.size(); i++) {
                sentIntents.add(sentPI);
                deliveredIntents.add(deliveredPI);
            }

            // Send the SMS
            smsManager.sendMultipartTextMessage(phoneNumber, null, parts,
                    sentIntents, deliveredIntents);

            Log.d(TAG, "SMS sent to " + phoneNumber);
        } catch (Exception e) {
            Log.e(TAG, "Failed to send SMS", e);
            Toast.makeText(getBaseContext(), "Failed to send SMS: " + e.getMessage(),
                    Toast.LENGTH_SHORT).show();

            // Clean up receivers if registration happened before exception
            try {
                unregisterReceiver(smsSentReceiver);
            } catch (IllegalArgumentException iae) {
                // Ignore
            }

            try {
                unregisterReceiver(smsDeliveredReceiver);
            } catch (IllegalArgumentException iae) {
                // Ignore
            }
        }
    }
}
