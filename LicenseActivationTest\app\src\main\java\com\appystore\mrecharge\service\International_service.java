package com.appystore.mrecharge.service;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.preference.PreferenceManager;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.appystore.mrecharge.R;
import com.appystore.mrecharge.activity.MainActivity;

public class International_service extends Service {
    private static final String TAG = "InternationalService";
    private static final String CHANNEL_ID = "InternationalServiceChannel";
    private static final int NOTIFICATION_ID = 2;

    private Handler handler;
    private Runnable runnable;
    private int runTime = 5000; // Default 5 seconds
    private SharedPreferences settings;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Service onCreate");

        // Initialize preferences
        settings = getSharedPreferences("serv", 0);

        // Get runtime from preferences (in seconds, convert to milliseconds)
        runTime = Integer.parseInt(getPreference("stime", "5")) * 1000;

        // Initialize handler for periodic tasks
        handler = new Handler();
        setupPeriodicTask();
    }

    private void setupPeriodicTask() {
        runnable = new Runnable() {
            @Override
            public void run() {
                if (settings.getInt("stop", 0) == 1) {
                    // Perform international service tasks here
                    processInternationalRequests();
                }

                // Schedule next run
                handler.postDelayed(this, runTime);
            }
        };
    }

    private void processInternationalRequests() {
        // Implementation for international service tasks
        Log.d(TAG, "Processing international requests");

        // Check for international recharge requests
        boolean bkashEnabled = settings.getInt("sima_servicem", 0) == 1;
        boolean rocketEnabled = settings.getInt("simb_servicem", 0) == 1;
        boolean nagadEnabled = settings.getInt("simb_servicem3", 0) == 1;

        if (bkashEnabled) {
            // Process bKash requests
            Log.d(TAG, "Processing bKash requests");
        }

        if (rocketEnabled) {
            // Process Rocket requests
            Log.d(TAG, "Processing Rocket requests");
        }

        if (nagadEnabled) {
            // Process Nagad requests
            Log.d(TAG, "Processing Nagad requests");
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "Service onStartCommand");

        if (intent == null) {
            return START_STICKY;
        }

        String action = intent.getStringExtra("serv");
        if (action != null && action.equals("off")) {
            stopService();
            return START_NOT_STICKY;
        }

        String notificationText = intent.getStringExtra("inputExtra");
        if (notificationText == null) {
            notificationText = "International Service is running";
        }

        startForegroundService(notificationText);

        // Start periodic task
        handler.postDelayed(runnable, runTime);

        return START_STICKY;
    }

    private void startForegroundService(String contentText) {
        createNotificationChannel();

        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("International Service Running")
                .setContentText(contentText)
                .setSmallIcon(R.drawable.ic_stat_name)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT);

        startForeground(NOTIFICATION_ID, builder.build());
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "International Service Channel",
                    NotificationManager.IMPORTANCE_DEFAULT
            );
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }

    private void stopService() {
        Log.d(TAG, "Stopping service");
        handler.removeCallbacks(runnable);
        stopForeground(true);
        stopSelf();
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "Service onDestroy");
        handler.removeCallbacks(runnable);
        super.onDestroy();
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    // Get preference with default value
    private String getPreference(String key, String defaultValue) {
        return PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).getString(key, defaultValue);
    }
}
