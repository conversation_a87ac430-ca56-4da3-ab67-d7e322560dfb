# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth"
    version: "23.2.0"
  }
  digests {
    sha256: "L\023\360\373\000\303z\373f\344\313\307]h\250\n\322\327=g\247\350[\r\244\225\200\250!\273\f9"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.4.0"
  }
  digests {
    sha256: "\341\220o\204Q/\036\245\344\311&\333\271\025x\232\330\367IO\244\356\232\322E\026?v\030\\\354\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.10"
  }
  digests {
    sha256: "U\351\211\305\022\270\t\ay\237\205C\t\363\274w\202\305\263\32192D-\003y\325\304rq\025\004"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.10"
  }
  digests {
    sha256: "\315\3434\033\241\212+\242b\260\267\317lU\262\f\220\350\3244\344,\232\023\346\243\367p\333\226Z\210"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.0"
  }
  digests {
    sha256: "\033\226\310\353\020\304\264\002\203\375\326\351\252t\377\377\005\372\344\361]T\366\033\246\235Q\177\315\024F\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.2"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.2"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.3"
  }
  digests {
    sha256: "d\326\352\032M\025\242\300\225\r\251\246\037I\352|Q&\216\347L;\035\335\304c\346\225TD\033$"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.4.0"
  }
  digests {
    sha256: "\316\\\223o\326h\024\263`/\\j^\222\231\021\377\227=K\005\366\336\231\226\332Yk\357\227\312\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.4"
  }
  digests {
    sha256: "\274<$1\335\244.\224\273\225\021\305\207\352\350\220\322v\344\252\3769:\215\247\260\001i\030m\257\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.0"
  }
  digests {
    sha256: "Y\305Na)\351&\034m\201\017\216\352Jn\342\364\231M\201b\261\315\237\310\214\234\204\311*\314\374"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.2"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.2"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.2"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.2"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.6.2"
  }
  digests {
    sha256: "\212\316\2311?\n\346\257G\031K\312\376(\363D\343c\364\322\223\370K+\227\264\201s[\004\336\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.2"
  }
  digests {
    sha256: "{\307\334\272\261v6\354\ao\022\257\344\320&q&\\8\224W\261\263f\263z\016\214\271\036-\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "I\217e\033O\221f\277w\017l\251z\353\250\351!\356\301\001\263\337\370\331\360\305\032\310\366\344\002\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials-play-services-auth"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "X0\246A8N\227\226A[0\363M\302Px-x\b\020\034\366\22264`\33199\240 \311"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "20.7.0"
  }
  digests {
    sha256: "\025\004\224\327\240R\253\252\254\233\270)\242\214\212\322j\303\312Y\305v\301ig\275\276(\252\252N\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.2"
  }
  digests {
    sha256: "\277\3378\016t\000\251\331P\313\244\336\334\025+\033\316aJ\20750\t\350Q\bt(\222|\302@"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.3.0"
  }
  digests {
    sha256: "\224\006jF\004~=Y>\266R8>wg\340c\003\205\354\327Q3\346\214!\022J\251k\215\302"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.4"
  }
  digests {
    sha256: "\253RK\030r]\220\243\006\r\247\\A\3241\3237\266\331\017^\2259\357\356\337!Zu\336\266?"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "20.1.0"
  }
  digests {
    sha256: "\222r:8\344\r:\312\037\2365\021YR\231\220!\251/\254$\2371a\3552\223\2214r\251\270"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.libraries.identity.googleid"
    artifactId: "googleid"
    version: "1.1.0"
  }
  digests {
    sha256: "\031J\301\374\031\206\335\037b\004o\2567\335\367~cw\017\334\037=4\272\2529|\373\364\321\221\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "integrity"
    version: "1.3.0"
  }
  digests {
    sha256: "6\253\374C\227P\270%\355\333?W\a\347\357\002\034K8\354\341\334r\202f\205\256\273\327\000L3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "core-common"
    version: "2.0.3"
  }
  digests {
    sha256: "C\003%_\025,Y\271T\221\233\264q\321N\345\376\322\337x5e\336\250R\213\b\b;\2474+"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.recaptcha"
    artifactId: "recaptcha"
    version: "18.6.1"
  }
  digests {
    sha256: "]\r\\\350i\332c\004\2754\342\027\273\367e`\034Q\306\036\305\305\177/!\233\232\210\024\321\370\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck-interop"
    version: "17.0.0"
  }
  digests {
    sha256: "\375y\334\340.\305\033\223\037\307\265\307,7\022\210\322F\b_\324\027]\272\340l\370\347vr|k"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth-interop"
    version: "20.0.0"
  }
  digests {
    sha256: "\300\237\332\337\240WI\315\177]h_\367\237<$\370\273\325+\241\310\033\216k\277M\000\230\301\312I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.26.0"
  }
  digests {
    sha256: "S\315\374\v\353-vo\340;x\360\261\035\002\005T\315A\230y\322\003\200\303\217\241\334\362\272\033P"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.2.1"
  }
  digests {
    sha256: "0\370\327\233x-(:\220\360\266\3676\221i\331\317V\000\221S\177\335`V\321\332\251\363\0037c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.1.1"
  }
  digests {
    sha256: "< T2\203(\203\036\266\346\233@\024\366\357\237\252\021\177\324\270\021\222\237:\221\323\3337_,\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.9.0"
  }
  digests {
    sha256: "2\252 k\232)\311\337^\332\223\240\222\317\263\260\271\023>#,\006+\252\210/\003\031\360\347\237\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.8.5"
  }
  digests {
    sha256: "#:\001I\3746\\\237n\333\326\203\317\342f\261\233\334w;\351\216\253\332\366\263\311$\264\216}\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "32.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "24.0.0"
  }
  digests {
    sha256: "o\"\247\220Z-0M(rK\v\254\257g\027\2016\223\377\267\374\333\234\277\331Ax\201S\2345"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "18.2.0"
  }
  digests {
    sha256: "\262\262\217k\241s\365\340\304\376=6\242\327\356R9\307\254\277AC\265I\354\3601\243H[5\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.1.0"
  }
  digests {
    sha256: "}\257\303\237\016\2505G3f\254\303F\367\346\177\310D?Ld]\231\234>\230{\312\326\270\214{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.9"
  }
  digests {
    sha256: "At\\[\217B}$C\220\025\270Oe\032\324 q\211\221\206}*\374&,&@\t\270\002\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.9"
  }
  digests {
    sha256: "\a\243 %\246[\b\356~\021\321M\3059\247X\266g\023\360\301\3219\000\250\025\231\316\255\272\364M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "17.2.0"
  }
  digests {
    sha256: "\336\001\036j;ya\336c\217\027/,\266k\243\004\352\251\211f*\236+\017H\337\367t\314\301f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.2.0"
  }
  digests {
    sha256: "\'%^\177\351pd\203\201k\025\215\262\\\363\031\366\242j\005f\376\377AY|\350\200z5\0167"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.9.0"
  }
  digests {
    sha256: "\213\205\363\212\250&\331\002\350\250\215*\371\334s\245\364?f\030r\004\205\361nt&\222\305\241\217o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.5.0"
  }
  digests {
    sha256: "\020\367m\365b\"SL{\370|\230d=j\017\333~\2354#\226\246y\303\025\341\0206\260E\361"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.5.0"
  }
  digests {
    sha256: "\002b\342\240\342\242\351\307&{\237z@XG\316cj\006\304\301lR\227l\266\215\367xf\270}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.5.0"
  }
  digests {
    sha256: "\230\202\204\030w\244C\177 \002\355\2355?uzTR\341;J\260\353\361\225\306<\317\016.\bG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.3.0"
  }
  digests {
    sha256: "\323\323~$\003\305#\245\316\341\230;\'\246\336z\330\334\273P/Dl?v\374\245\016\327<\345b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.3.0"
  }
  digests {
    sha256: "\213[\323\254\357\001\352x\032\205E\275\367\020\261\300a\202Avi\241\246\214\320\256JSl\332\350\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.android.volley"
    artifactId: "volley"
    version: "1.2.1"
  }
  digests {
    sha256: "\333\360J#\377\221\266quq\257\347\"\376K\325wbW\365\221\021\327\302\315\373:\235_\210\210\356"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 24
  library_dep_index: 52
  library_dep_index: 47
  library_dep_index: 23
  library_dep_index: 22
  library_dep_index: 53
  library_dep_index: 55
  library_dep_index: 56
  library_dep_index: 58
  library_dep_index: 59
  library_dep_index: 60
  library_dep_index: 63
  library_dep_index: 61
  library_dep_index: 5
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 3
  library_dep_index: 9
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 4
  library_dep_index: 5
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 8
  library_dep_index: 3
  library_dep_index: 9
}
library_dependencies {
  library_index: 10
  library_dep_index: 3
}
library_dependencies {
  library_index: 11
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 43
  library_dep_index: 5
  library_dep_index: 26
}
library_dependencies {
  library_index: 12
  library_dep_index: 5
}
library_dependencies {
  library_index: 13
  library_dep_index: 3
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 37
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 34
  library_dep_index: 28
  library_dep_index: 35
}
library_dependencies {
  library_index: 14
  library_dep_index: 3
}
library_dependencies {
  library_index: 15
  library_dep_index: 3
  library_dep_index: 14
}
library_dependencies {
  library_index: 16
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 17
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 13
  library_dep_index: 34
  library_dep_index: 28
  library_dep_index: 35
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 41
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 19
  library_dep_index: 7
  library_dep_index: 20
  library_dep_index: 6
  library_dep_index: 41
}
library_dependencies {
  library_index: 20
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 18
  library_dep_index: 21
}
library_dependencies {
  library_index: 21
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 41
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 2
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 28
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 36
  library_dep_index: 39
  library_dep_index: 5
}
library_dependencies {
  library_index: 25
  library_dep_index: 3
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 13
  library_dep_index: 28
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 36
  library_dep_index: 33
  library_dep_index: 5
  library_dep_index: 18
  library_dep_index: 5
}
library_dependencies {
  library_index: 26
  library_dep_index: 3
  library_dep_index: 11
  library_dep_index: 5
  library_dep_index: 11
}
library_dependencies {
  library_index: 27
  library_dep_index: 5
  library_dep_index: 5
}
library_dependencies {
  library_index: 28
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 13
  library_dep_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 29
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 30
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 13
  library_dep_index: 34
  library_dep_index: 28
  library_dep_index: 35
}
library_dependencies {
  library_index: 30
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 13
  library_dep_index: 34
  library_dep_index: 28
  library_dep_index: 35
}
library_dependencies {
  library_index: 31
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 13
  library_dep_index: 34
  library_dep_index: 28
  library_dep_index: 35
}
library_dependencies {
  library_index: 32
  library_dep_index: 3
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 3
}
library_dependencies {
  library_index: 34
  library_dep_index: 13
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 13
  library_dep_index: 28
  library_dep_index: 35
}
library_dependencies {
  library_index: 35
  library_dep_index: 3
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 28
  library_dep_index: 36
  library_dep_index: 5
  library_dep_index: 17
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 13
  library_dep_index: 34
  library_dep_index: 28
}
library_dependencies {
  library_index: 36
  library_dep_index: 3
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 5
}
library_dependencies {
  library_index: 37
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 32
  library_dep_index: 9
}
library_dependencies {
  library_index: 38
  library_dep_index: 3
  library_dep_index: 11
  library_dep_index: 29
  library_dep_index: 28
}
library_dependencies {
  library_index: 39
  library_dep_index: 3
  library_dep_index: 11
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 3
  library_dep_index: 11
  library_dep_index: 2
}
library_dependencies {
  library_index: 41
  library_dep_index: 5
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 5
}
library_dependencies {
  library_index: 43
  library_dep_index: 3
  library_dep_index: 2
}
library_dependencies {
  library_index: 44
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 18
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 5
  library_dep_index: 44
}
library_dependencies {
  library_index: 46
  library_dep_index: 24
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 48
  library_dep_index: 23
  library_dep_index: 50
  library_dep_index: 22
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 48
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 49
  library_dep_index: 2
  library_dep_index: 48
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 50
  library_dep_index: 48
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 51
  library_dep_index: 5
  library_dep_index: 41
}
library_dependencies {
  library_index: 52
  library_dep_index: 3
}
library_dependencies {
  library_index: 53
  library_dep_index: 23
  library_dep_index: 22
  library_dep_index: 54
}
library_dependencies {
  library_index: 55
  library_dep_index: 23
  library_dep_index: 22
  library_dep_index: 53
  library_dep_index: 5
  library_dep_index: 17
  library_dep_index: 21
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 58
  library_dep_index: 48
  library_dep_index: 22
}
library_dependencies {
  library_index: 59
  library_dep_index: 23
  library_dep_index: 22
  library_dep_index: 56
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 21
  library_dep_index: 61
  library_dep_index: 56
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 5
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 61
  library_dep_index: 56
  library_dep_index: 3
  library_dep_index: 62
}
library_dependencies {
  library_index: 63
  library_dep_index: 60
  library_dep_index: 41
  library_dep_index: 61
  library_dep_index: 56
}
library_dependencies {
  library_index: 64
  library_dep_index: 25
  library_dep_index: 3
  library_dep_index: 65
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 26
  library_dep_index: 68
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 24
  library_dep_index: 13
  library_dep_index: 28
  library_dep_index: 37
  library_dep_index: 72
  library_dep_index: 36
  library_dep_index: 5
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 3
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 66
  library_dep_index: 67
  library_dep_index: 64
}
library_dependencies {
  library_index: 66
  library_dep_index: 3
  library_dep_index: 11
  library_dep_index: 2
}
library_dependencies {
  library_index: 67
  library_dep_index: 66
  library_dep_index: 10
  library_dep_index: 2
}
library_dependencies {
  library_index: 68
  library_dep_index: 3
}
library_dependencies {
  library_index: 69
  library_dep_index: 3
  library_dep_index: 11
  library_dep_index: 40
}
library_dependencies {
  library_index: 70
  library_dep_index: 3
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 70
  library_dep_index: 70
}
library_dependencies {
  library_index: 72
  library_dep_index: 3
}
library_dependencies {
  library_index: 73
  library_dep_index: 74
  library_dep_index: 62
  library_dep_index: 25
  library_dep_index: 3
  library_dep_index: 64
  library_dep_index: 75
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 11
  library_dep_index: 69
  library_dep_index: 79
  library_dep_index: 12
  library_dep_index: 24
  library_dep_index: 13
  library_dep_index: 83
  library_dep_index: 72
  library_dep_index: 84
  library_dep_index: 66
  library_dep_index: 85
}
library_dependencies {
  library_index: 74
  library_dep_index: 5
  library_dep_index: 41
  library_dep_index: 6
  library_dep_index: 42
}
library_dependencies {
  library_index: 75
  library_dep_index: 3
}
library_dependencies {
  library_index: 76
  library_dep_index: 3
  library_dep_index: 11
  library_dep_index: 40
  library_dep_index: 2
}
library_dependencies {
  library_index: 77
  library_dep_index: 64
  library_dep_index: 78
  library_dep_index: 11
  library_dep_index: 37
}
library_dependencies {
  library_index: 78
  library_dep_index: 3
}
library_dependencies {
  library_index: 79
  library_dep_index: 11
  library_dep_index: 2
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 3
  library_dep_index: 11
  library_dep_index: 81
  library_dep_index: 38
  library_dep_index: 52
  library_dep_index: 82
}
library_dependencies {
  library_index: 81
  library_dep_index: 3
}
library_dependencies {
  library_index: 82
  library_dep_index: 3
}
library_dependencies {
  library_index: 83
  library_dep_index: 3
  library_dep_index: 11
  library_dep_index: 40
  library_dep_index: 2
}
library_dependencies {
  library_index: 84
  library_dep_index: 3
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 79
}
library_dependencies {
  library_index: 85
  library_dep_index: 3
  library_dep_index: 24
  library_dep_index: 83
  library_dep_index: 11
  library_dep_index: 2
}
library_dependencies {
  library_index: 86
  library_dep_index: 87
}
library_dependencies {
  library_index: 87
  library_dep_index: 88
  library_dep_index: 41
}
library_dependencies {
  library_index: 88
  library_dep_index: 89
}
library_dependencies {
  library_index: 89
  library_dep_index: 41
  library_dep_index: 6
}
library_dependencies {
  library_index: 90
  library_dep_index: 86
  library_dep_index: 91
}
library_dependencies {
  library_index: 92
  library_dep_index: 87
  library_dep_index: 41
}
library_dependencies {
  library_index: 93
  library_dep_index: 0
  library_dep_index: 63
  library_dep_index: 94
  library_dep_index: 60
  library_dep_index: 98
  library_dep_index: 103
}
library_dependencies {
  library_index: 94
  library_dep_index: 60
  library_dep_index: 63
  library_dep_index: 61
  library_dep_index: 95
  library_dep_index: 98
  library_dep_index: 101
  library_dep_index: 99
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 104
  library_dep_index: 105
  library_dep_index: 3
  library_dep_index: 96
  library_dep_index: 100
  library_dep_index: 97
  library_dep_index: 48
  library_dep_index: 23
  library_dep_index: 106
  library_dep_index: 107
  library_dep_index: 22
  library_dep_index: 62
  library_dep_index: 5
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
  library_dep_index: 97
  library_dep_index: 100
  library_dep_index: 3
}
library_dependencies {
  library_index: 96
  library_dep_index: 3
}
library_dependencies {
  library_index: 97
  library_dep_index: 96
  library_dep_index: 3
  library_dep_index: 57
  library_dep_index: 98
  library_dep_index: 99
}
library_dependencies {
  library_index: 98
  library_dep_index: 3
}
library_dependencies {
  library_index: 99
  library_dep_index: 3
  library_dep_index: 98
}
library_dependencies {
  library_index: 100
  library_dep_index: 96
  library_dep_index: 97
  library_dep_index: 98
  library_dep_index: 101
  library_dep_index: 3
}
library_dependencies {
  library_index: 101
  library_dep_index: 3
  library_dep_index: 98
}
library_dependencies {
  library_index: 102
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 103
  library_dep_index: 104
  library_dep_index: 5
  library_dep_index: 22
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 63
  library_dep_index: 61
}
library_dependencies {
  library_index: 104
  library_dep_index: 22
  library_dep_index: 56
}
library_dependencies {
  library_index: 105
  library_dep_index: 23
  library_dep_index: 56
}
library_dependencies {
  library_index: 106
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 107
  library_dep_index: 80
  library_dep_index: 23
}
library_dependencies {
  library_index: 108
  library_dep_index: 12
  library_dep_index: 11
  library_dep_index: 29
  library_dep_index: 34
  library_dep_index: 109
  library_dep_index: 113
  library_dep_index: 32
  library_dep_index: 9
  library_dep_index: 5
  library_dep_index: 17
}
library_dependencies {
  library_index: 109
  library_dep_index: 110
  library_dep_index: 111
  library_dep_index: 5
  library_dep_index: 17
}
library_dependencies {
  library_index: 110
  library_dep_index: 3
  library_dep_index: 41
}
library_dependencies {
  library_index: 111
  library_dep_index: 12
  library_dep_index: 15
  library_dep_index: 110
  library_dep_index: 112
  library_dep_index: 113
}
library_dependencies {
  library_index: 112
  library_dep_index: 3
  library_dep_index: 5
}
library_dependencies {
  library_index: 113
  library_dep_index: 3
  library_dep_index: 112
  library_dep_index: 5
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 64
  dependency_index: 73
  dependency_index: 25
  dependency_index: 77
  dependency_index: 86
  dependency_index: 90
  dependency_index: 87
  dependency_index: 92
  dependency_index: 3
  dependency_index: 93
  dependency_index: 94
  dependency_index: 48
  dependency_index: 108
  dependency_index: 114
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
