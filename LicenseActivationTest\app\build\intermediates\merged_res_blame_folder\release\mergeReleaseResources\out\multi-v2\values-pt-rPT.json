{"logs": [{"outputFile": "com.appystore.mrecharge.app-mergeReleaseResources-45:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\5876d8f2a399c27f40b853a1096d16b3\\transformed\\play-services-basement-18.4.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5798", "endColumns": "144", "endOffsets": "5938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\1735b8cdd7f63af0f6534fc72d24b693\\transformed\\appcompat-1.7.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,12689", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,12770"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\5ce42f50b8e9cd31ff8c0bdc3b462bc2\\transformed\\material-1.12.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1075,1139,1231,1310,1375,1465,1529,1597,1659,1732,1796,1850,1976,2034,2096,2150,2226,2369,2456,2536,2635,2721,2803,2942,3024,3106,3242,3329,3409,3465,3516,3582,3657,3737,3808,3887,3960,4037,4106,4180,4287,4380,4457,4550,4648,4722,4803,4902,4955,5039,5105,5194,5282,5344,5408,5471,5539,5655,5763,5870,5972,6032,6087,6173,6256,6335", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "268,349,429,511,610,706,809,929,1010,1070,1134,1226,1305,1370,1460,1524,1592,1654,1727,1791,1845,1971,2029,2091,2145,2221,2364,2451,2531,2630,2716,2798,2937,3019,3101,3237,3324,3404,3460,3511,3577,3652,3732,3803,3882,3955,4032,4101,4175,4282,4375,4452,4545,4643,4717,4798,4897,4950,5034,5100,5189,5277,5339,5403,5466,5534,5650,5758,5865,5967,6027,6082,6168,6251,6330,6409"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3293,3374,3454,3536,3635,4463,4566,4686,7217,7277,7341,7747,7826,7891,7981,8045,8113,8175,8248,8312,8366,8492,8550,8612,8666,8742,8885,8972,9052,9151,9237,9319,9458,9540,9622,9758,9845,9925,9981,10032,10098,10173,10253,10324,10403,10476,10553,10622,10696,10803,10896,10973,11066,11164,11238,11319,11418,11471,11555,11621,11710,11798,11860,11924,11987,12055,12171,12279,12386,12488,12548,12603,12775,12858,12937", "endLines": "5,35,36,37,38,39,47,48,49,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "318,3369,3449,3531,3630,3726,4561,4681,4762,7272,7336,7428,7821,7886,7976,8040,8108,8170,8243,8307,8361,8487,8545,8607,8661,8737,8880,8967,9047,9146,9232,9314,9453,9535,9617,9753,9840,9920,9976,10027,10093,10168,10248,10319,10398,10471,10548,10617,10691,10798,10891,10968,11061,11159,11233,11314,11413,11466,11550,11616,11705,11793,11855,11919,11982,12050,12166,12274,12381,12483,12543,12598,12684,12853,12932,13011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\5846cdb8710318d8515685e81b5b854c\\transformed\\credentials-1.2.0-rc01\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,172", "endColumns": "116,121", "endOffsets": "167,289"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3054,3171", "endColumns": "116,121", "endOffsets": "3166,3288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\51f01dee9f3222424bc9040047ad476b\\transformed\\play-services-base-18.3.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4767,4872,5035,5163,5271,5439,5567,5689,5943,6131,6239,6409,6540,6699,6877,6945,7014", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "4867,5030,5158,5266,5434,5562,5684,5793,6126,6234,6404,6535,6694,6872,6940,7009,7096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\534e5608d8b8e741317680d9a7d159af\\transformed\\core-1.13.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "40,41,42,43,44,45,46,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3731,3828,3930,4029,4129,4236,4342,13016", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3823,3925,4024,4124,4231,4337,4458,13112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\c20dbc919d74ed6428f648e519a57160\\transformed\\browser-1.4.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "68,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "7101,7433,7532,7644", "endColumns": "115,98,111,102", "endOffsets": "7212,7527,7639,7742"}}]}]}