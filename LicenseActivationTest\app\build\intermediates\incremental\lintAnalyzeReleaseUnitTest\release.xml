<variant
    name="release"
    package="com.appystore.mrecharge"
    minSdkVersion="24"
    targetSdkVersion="35"
    mergedManifest="build\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    partialResultsDir="build\intermediates\unit_test_lint_partial_results\release\lintAnalyzeReleaseUnitTest\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.14\transforms\839ab44d898c93cde484be25e300c581\transformed\desugar_jdk_libs_configuration-2.1.3-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.14\transforms\f9d6071d3a87451a98a1d0c7279bb7dc\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifests="src\test\AndroidManifest.xml"
        javaDirectories="src\test\java;src\testRelease\java;src\test\kotlin;src\testRelease\kotlin"
        assetsDirectories="src\test\assets;src\testRelease\assets"
        unitTest="true"/>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      type="UNIT_TEST">
  </artifact>
</variant>
